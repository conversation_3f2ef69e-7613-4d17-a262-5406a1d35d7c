import * as THREE from 'three';
import { SpatialGrid } from './spatial_grid';
import { ElMessage } from 'element-plus';

// 定义封闭图形对象的接口
interface ClosedShape {
    id: string;
    objectId?: number; // CAD对象的递增ID
    segments: Array<{
        start: THREE.Vector3;
        end: THREE.Vector3;
        line: THREE.Mesh | THREE.Line;
    }>;
    description: string;
    group: THREE.Group;
    isSelected: boolean;
    currentDirectionSegmentIndex?: number; // 当前方向线条的索引
    directionReversed?: boolean; // 方向是否翻转
    directionIndicator?: THREE.Mesh; // 方向指示器（小圆点）
    userData?: {
        direction?: number;
        area?: number; // 房间分区面积
        crowdDensity?: number; // 门的人流量
        [key: string]: any;
    };
}

// 绘制模式枚举
enum DrawingMode {
    MANUAL = 'manual',        // 手动绘制模式
    AUTO_DETECT = 'auto'      // 自动识别模式
}

export default class FreeDrawManager {

    static instance: FreeDrawManager | null = null;
    private scene: THREE.Scene | null = null;
    private camera: THREE.OrthographicCamera | null = null;
    private viewer: any = null; // 存储Viewer实例
    private container: HTMLElement | null = null; // 存储容器元素
    private isDrawing: boolean = false;
    private pointPositions: THREE.Vector3[] = [];
    private currentPoint: THREE.Vector3 | null = null;
    private previewLine: THREE.Line | null = null;
    private drawnLines: (THREE.Line | THREE.Mesh)[] = [];
    private pointMarkers: THREE.Mesh[] = [];

    // DXF点吸附相关
    private spatialGrid: SpatialGrid;
    private snapDistance: number = 100; // 吸附距离
    private snapIndicator: THREE.Mesh | null = null; // 吸附指示器

    // 封闭图形管理
    private closedShapes: ClosedShape[] = [];
    private selectedShape: ClosedShape | null = null;
    private raycaster: THREE.Raycaster;
    private mouse: THREE.Vector2;

    // 保存绑定后的事件处理函数引用
    private boundMouseDown: (event: MouseEvent) => void;
    private boundMouseMove: (event: MouseEvent) => void;
    private boundMouseUp: (event: MouseEvent) => void;
    private boundKeyDown: (event: KeyboardEvent) => void;
    private boundShapeClick: (event: MouseEvent) => void;

    // 右键操作状态
    private rightMouseDown: boolean = false;
    private rightMouseStartPos: { x: number; y: number } | null = null;
    private dragThreshold: number = 5;

    // 对话框防抖状态
    private dialogJustClosed: boolean = false;
    private dialogCloseTimeout: number | null = null;

    // 分区绘制相关
    private partitionGroups: THREE.Group[] = []; // 存储分区组
    private partitionColors: number[] = [
        0xff0000, // 红色
        0xffff00, // 黄色
        0x000080, // 深蓝色
        0x808080, // 灰色
        0x800080, // 紫色
        0x00bfff, // 天蓝色
        0xffc0cb  // 粉红色
    ];

    // DXF封闭图形识别相关
    private dxfClosedShapes: any[] = []; // 存储DXF中的封闭图形
    private onjectMap: Map<string, number> = new Map(); // 存储对象的Map

    // 绘制模式管理
    private currentDrawingMode: DrawingMode = DrawingMode.MANUAL; // 默认为手动绘制模式
    private modeToggleDebounce: boolean = false; // 模式切换防抖状态
    private hotkeyHandler: ((event: KeyboardEvent) => void) | null = null; // 存储快捷键处理函数

    // 全局属性
    private globalHeight: string = ''; // 全局层高
    private globalArea: string = ''; // 全局面积
    private globalInputContainer: HTMLElement | null = null; // 全局输入容器

    // CAD对象ID计数器
    private cadObjectCounters: Map<string, number> = new Map(); // 每种CAD对象类型的计数器

    private constructor() {
        this.boundMouseDown = this.onMouseDown.bind(this);
        this.boundMouseMove = this.onMouseMove.bind(this);
        this.boundMouseUp = this.onMouseUp.bind(this);
        this.boundKeyDown = this.onKeyDown.bind(this);
        this.boundShapeClick = this.onShapeClick.bind(this);
        this.spatialGrid = new SpatialGrid(1000);
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        this.initOnjectMap();
    };

    private initOnjectMap(): void {
        console.log('init');
        this.onjectMap.set('门', 0);
        this.onjectMap.set('消防栓', 0);
        this.onjectMap.set('柱子', 0);
        this.onjectMap.set('障碍物', 0);
        this.onjectMap.set('墙', 0);
        this.onjectMap.set('玻璃墙', 0);
        this.onjectMap.set('分水器', 0);
        this.onjectMap.set('窗', 0);
    }

    /**
     * 设置Viewer实例和容器
     */
    public setViewer(viewer: any, container: HTMLElement): void {
        this.viewer = viewer;
        this.container = container;
        this.scene = viewer.getScene();
        this.camera = viewer.getCamera();
        
        // 确保点击监听器在初始化时就被添加
        this.ensureShapeClickListener();
        
        // 自动绑定快捷键（包括加载功能）
        this.bindHotkeys();
        
        // 创建全局输入区域
        this.createGlobalInputArea();
        
        console.log('Viewer已设置，快捷键已绑定（包括L键加载功能）');
    }

    /**
     * 确保图形点击监听器存在
     */
    private ensureShapeClickListener(): void {
        // 先移除可能存在的监听器，避免重复添加
        window.removeEventListener('click', this.boundShapeClick);
        // 重新添加监听器
        window.addEventListener('click', this.boundShapeClick);
    }

    /**
     * 创建全局输入区域（右上角）
     */
    private createGlobalInputArea(): void {
        // 移除现有的全局输入容器
        if (this.globalInputContainer) {
            this.globalInputContainer.remove();
        }

        const container = document.createElement('div');
        container.id = 'global-input-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20vw;
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid #007bff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 9999;
            width: 200px;
            font-family: Arial, sans-serif;
            padding: 12px;
        `;

        // 标题
        const title = document.createElement('div');
        title.textContent = '全局属性';
        title.style.cssText = `
            font-size: 14px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
            text-align: center;
        `;

        // 层高输入
        const heightWrapper = document.createElement('div');
        heightWrapper.style.cssText = `
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        `;

        const heightLabel = document.createElement('span');
        heightLabel.textContent = '层高(mm)';
        heightLabel.style.cssText = `
            font-size: 12px;
            color: #333;
            min-width: 40px;
        `;

        const heightInput = document.createElement('input');
        heightInput.type = 'text';
        heightInput.placeholder = '输入层高(mm)';
        heightInput.value = this.globalHeight;
        heightInput.style.cssText = `
            flex: 1;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            outline: none;
            width: 124px;
        `;

        heightInput.addEventListener('input', (e) => {
            this.globalHeight = (e.target as HTMLInputElement).value;
        });

        // 添加自动转换功能（层高：m → mm）
        heightInput.addEventListener('blur', (e) => {
            const value = (e.target as HTMLInputElement).value.trim();
            const converted = this.convertToMillimeters(value);
            if (converted !== value) {
                (e.target as HTMLInputElement).value = converted;
                this.globalHeight = converted;
            }
        });

        // 面积输入
        const areaWrapper = document.createElement('div');
        areaWrapper.style.cssText = `
            margin-bottom: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        `;

        const areaLabel = document.createElement('span');
        areaLabel.textContent = '面积(mm²)';
        areaLabel.style.cssText = `
            font-size: 12px;
            color: #333;
            min-width: 40px;
        `;

        const areaInput = document.createElement('input');
        areaInput.type = 'text';
        areaInput.placeholder = '输入面积(mm²)';
        areaInput.value = this.globalArea;
        areaInput.style.cssText = `
            flex: 1;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            outline: none;
            width: 124px;
        `;

        areaInput.addEventListener('input', (e) => {
            this.globalArea = (e.target as HTMLInputElement).value;
        });

        // 添加自动转换功能（面积：m² → mm²）
        areaInput.addEventListener('blur', (e) => {
            const value = (e.target as HTMLInputElement).value.trim();
            const converted = this.convertToSquareMillimeters(value);
            if (converted !== value) {
                (e.target as HTMLInputElement).value = converted;
                this.globalArea = converted;
            }
        });

        // 组装容器
        heightWrapper.appendChild(heightLabel);
        heightWrapper.appendChild(heightInput);
        areaWrapper.appendChild(areaLabel);
        areaWrapper.appendChild(areaInput);

        container.appendChild(title);
        container.appendChild(heightWrapper);
        container.appendChild(areaWrapper);

        document.body.appendChild(container);
        this.globalInputContainer = container;

        console.log('全局输入区域已创建');
    }

    /**
     * 更新全局输入区域的值
     */
    private updateGlobalInputValues(): void {
        if (!this.globalInputContainer) return;

        const heightInput = this.globalInputContainer.querySelector('input[placeholder="输入层高(mm)"]') as HTMLInputElement;
        const areaInput = this.globalInputContainer.querySelector('input[placeholder="输入面积(mm²)"]') as HTMLInputElement;

        if (heightInput) {
            heightInput.value = this.globalHeight;
        }
        if (areaInput) {
            areaInput.value = this.globalArea;
        }
    }

    /**
     * 获取CAD对象的递增ID
     * @param itemName CAD对象名称
     * @returns 递增的ID
     */
    private getNextCadObjectId(itemName: string): number {
        const currentCount = this.cadObjectCounters.get(itemName) || 0;
        const nextId = currentCount + 1;
        this.cadObjectCounters.set(itemName, nextId);
        return nextId;
    }

    /**
     * 重置所有CAD对象计数器
     */
    private resetCadObjectCounters(): void {
        this.cadObjectCounters.clear();
    }

    /**
     * 转换长度单位：m → mm
     * @param value 输入值
     * @returns 转换后的值（字符串）
     */
    private convertToMillimeters(value: string): string {
        if (!value) return value;
        
        // 匹配以m开头的数字（如：3m、3.5m等）
        const match = value.match(/^(\d+(?:\.\d+)?)m$/i);
        if (match) {
            const meters = parseFloat(match[1]);
            const millimeters = meters * 1000;
            return millimeters.toString();
        }
        
        return value;
    }

    /**
     * 转换面积单位：m² → mm²
     * @param value 输入值  
     * @returns 转换后的值（字符串）
     */
    private convertToSquareMillimeters(value: string): string {
        if (!value) return value;
        
        // 匹配以m²开头的数字（如：10m²、15.5m²等）
        const match = value.match(/^(\d+(?:\.\d+)?)m[²2]?$/i);
        if (match) {
            const squareMeters = parseFloat(match[1]);
            const squareMillimeters = squareMeters * 1000000; // 1m² = 1,000,000mm²
            return squareMillimeters.toString();
        }
        
        return value;
    }

    /**
     * 检查是否为CAD对象
     * @param description 描述
     * @returns 是否为CAD对象
     */
    private isCadObject(description: string): boolean {
        const cadLabels = ['门', '消防栓', '柱子', '障碍物', '墙', '玻璃墙', '分水器', '窗'];
        return cadLabels.some(label => description.includes(label));
    }

    /**
     * 初始化DXF数据点到空间网格
     */
    public initializeDxfDataPoints(): void {
        if (!this.viewer) {
            console.warn('Viewer not available for DXF data points initialization');
            return;
        }

        const dxfDataPoint = this.viewer.getDxfDataPoint();
        if (dxfDataPoint && dxfDataPoint.size > 0) {
            this.spatialGrid.buildFromDxfDataPoints(dxfDataPoint);
            const stats = this.spatialGrid.getStats();
            console.log('DXF数据点空间网格初始化完成:', stats);
        } else {
            console.warn('No DXF data points available');
        }
    }

    /**
     * 初始化DXF封闭图形数据
     */
    private initializeDxfClosedShapes(): void {
        if (!this.viewer) {
            console.warn('Viewer not available for DXF closed shapes initialization');
            return;
        }

        // 获取DXF中的封闭图形
        if (typeof this.viewer.getClosedShapes === 'function') {
            this.dxfClosedShapes = this.viewer.getClosedShapes();
            
            // 显示统计信息（包括面积过滤信息）
            this.showDxfShapeStats();
        } else {
            console.warn('Viewer does not support getClosedShapes method');
            this.dxfClosedShapes = [];
        }
    }

    /**
     * 检查点是否在DXF封闭图形内
     */
    private checkPointInDxfShape(worldPosition: THREE.Vector3): any | null {
        if (!this.viewer || typeof this.viewer.findShapeByPoint !== 'function') {
            return null;
        }

        const point = { x: worldPosition.x, y: worldPosition.y };
        return this.viewer.findShapeByPoint(point);
    }

    /**
     * 自动绘制DXF封闭图形
     */
    private autoDrawDxfShape(shape: any): void {
        if (!shape || !shape.points || shape.points.length < 3) {
            console.warn('Invalid shape data for auto drawing');
            return;
        }

        // 清除当前绘制状态
        this.pointPositions = [];
        this.currentPoint = null;
        this.clearPreviewLine();

        // 转换DXF点为THREE.Vector3，从左上角开始并确保顺时针顺序
        const points = this.arrangePointsFromTopLeftClockwise(shape.points.map((p: any) => 
            new THREE.Vector3(p.x, p.y, p.z || 0)
        ));

        for (let i = 0; i < points.length; i++) {
            const startPoint = points[i];
            const endPoint = points[(i + 1) % points.length]; // 闭合到第一个点

            this.addPointMarker(startPoint);
            this.pointPositions.push(startPoint);

            if (i < points.length - 1 || points.length > 2) {
                this.drawPermanentLine(startPoint, endPoint);
            }
        }

        this.createClosedShape();

        // 结束绘制模式
        this.endDrawingMode();

        setTimeout(() => {
            const lastShape = this.closedShapes[this.closedShapes.length - 1];
            if (lastShape) {
                // 预设墙体信息
                lastShape.description = '墙';
                if (!lastShape.userData) {
                    lastShape.userData = {};
                }
                lastShape.userData.direction = -1;
                
                // 为CAD对象分配递增ID（仅在首次创建时分配）
                if (this.isCadObject(lastShape.description) && lastShape.objectId === undefined) {
                    lastShape.objectId = this.getNextCadObjectId(lastShape.description);
                }
                
                this.setShapeSelected(lastShape, true);
                this.showDescriptionDialog(lastShape, window.innerWidth / 2, window.innerHeight / 2);
            }
        }, 300);
    }

    /**
     * 从左上角开始按顺时针方向排列点
     */
    private arrangePointsFromTopLeftClockwise(points: THREE.Vector3[]): THREE.Vector3[] {
        if (points.length < 3) return points;

        // 1. 找到左上角的点（最小x，如果x相同则最大y）
        let topLeftIndex = 0;
        let topLeftPoint = points[0];
        
        for (let i = 1; i < points.length; i++) {
            const currentPoint = points[i];
            
            // 比较规则：x坐标更小，或x相同时y坐标更大（因为CAD坐标系y向上为正）
            if (currentPoint.x < topLeftPoint.x || 
                (Math.abs(currentPoint.x - topLeftPoint.x) < 0.001 && currentPoint.y > topLeftPoint.y)) {
                topLeftIndex = i;
                topLeftPoint = currentPoint;
            }
        }

        // 2. 从左上角点开始重新排列数组
        const reorderedPoints = [
            ...points.slice(topLeftIndex),
            ...points.slice(0, topLeftIndex)
        ];

        // 3. 确保是顺时针方向
        const clockwisePoints = this.ensureClockwiseOrder(reorderedPoints);

        // 4. 验证结果
        this.validatePointOrder(clockwisePoints);

        return clockwisePoints;
    }

    /**
     * 确保点的顺序为顺时针
     */
    private ensureClockwiseOrder(points: THREE.Vector3[]): THREE.Vector3[] {
        if (points.length < 3) return points;

        // 计算多边形的有向面积（使用shoelace公式）
        let signedArea = 0;
        for (let i = 0; i < points.length; i++) {
            const j = (i + 1) % points.length;
            signedArea += (points[j].x - points[i].x) * (points[j].y + points[i].y);
        }

        // 如果有向面积为正，则为逆时针，需要翻转为顺时针
        if (signedArea > 0) {
            console.log('翻转为顺时针方向');
            return [points[0], ...points.slice(1).reverse()];
        }

        return points;
    }

    /**
     * 验证点的顺序
     */
    private validatePointOrder(points: THREE.Vector3[]): void {
        if (points.length < 3) return;
        // 验证是否为顺时针
        let signedArea = 0;
        for (let i = 0; i < points.length; i++) {
            const j = (i + 1) % points.length;
            signedArea += (points[j].x - points[i].x) * (points[j].y + points[i].y);
        }
    }

    /**
     * 添加点标记
     */
    private addPointMarker(position: THREE.Vector3): void {
        const geometry = new THREE.CircleGeometry(50, 32);
        const material = new THREE.MeshBasicMaterial({
            color: 0xff0000,
            transparent: true,
            opacity: 0.8
        });

        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.copy(position);
        mesh.renderOrder = 1001;
        mesh.position.z = 0.2;

        this.scene?.add(mesh);
        this.pointMarkers.push(mesh);
    }

    private setupScene(): void {
        if (this.viewer) {
            this.scene = this.viewer.getScene();
        }
    }

    public static getInstance(): FreeDrawManager {
        if (!this.instance) {
            this.instance = new FreeDrawManager();
        }
        return this.instance;
    }

    public startDrawingMode(): void {
        if (!this.viewer) {
            console.error('Viewer not set. Please call setViewer() first.');
            return;
        }

        if (!this.scene) {
            this.setupScene();
        }
        if (!this.camera) {
            this.camera = this.viewer.getCamera();
        }

        // 初始化DXF数据点到空间网格
        this.initializeDxfDataPoints();

        // 初始化DXF封闭图形数据
        this.initializeDxfClosedShapes();

        // 清理任何未完成的绘制状态
        this.clearIncompleteDrawing();

        // 智能状态处理
        if (this.pointPositions.length === 1 && this.drawnLines.length === 0) {
            this.pointPositions = [];
            this.currentPoint = null;
        } else if (this.pointPositions.length > 1) {
            this.currentPoint = this.pointPositions[this.pointPositions.length - 1];
        } else {
            this.pointPositions = [];
            this.currentPoint = null;
        }

        // 清除预览线和吸附指示器
        this.clearPreviewLine();
        this.clearSnapIndicator();
        this.isDrawing = true;
        window.addEventListener('mousedown', this.boundMouseDown);
        window.addEventListener('mousemove', this.boundMouseMove);
        window.addEventListener('mouseup', this.boundMouseUp);
        window.addEventListener('keydown', this.boundKeyDown);

        window.removeEventListener('click', this.boundShapeClick);

        console.log('start drawing');
        
        // 显示当前绘制模式
        this.showModeStatus();
    }
    public endDrawingMode(): void {
        this.isDrawing = false;
        window.removeEventListener('mousedown', this.boundMouseDown);
        window.removeEventListener('mousemove', this.boundMouseMove);
        window.removeEventListener('mouseup', this.boundMouseUp);
        window.removeEventListener('keydown', this.boundKeyDown);

        window.addEventListener('click', this.boundShapeClick);

        this.clearPreviewLine();
        this.clearSnapIndicator();

        // 清理未完成的绘制状态
        this.clearIncompleteDrawing();

        this.rightMouseDown = false;
        this.rightMouseStartPos = null;

    }

    /**
     * 清理未完成的绘制状态
     */
    private clearIncompleteDrawing(): void {
        // 清理条件：有未完成的点或线段（不足3个点构成封闭图形）
        const hasIncompleteDrawing = (
            this.pointPositions.length > 0 && this.pointPositions.length < 3
        ) || (
            this.drawnLines.length > 0 && this.drawnLines.length < 3
        ) || (
            this.pointMarkers.length > 0 && this.pointMarkers.length < 3
        );

        if (hasIncompleteDrawing) {

            // 清理所有未完成的线段
            this.drawnLines.forEach(line => {
                this.scene?.remove(line);
            });
            this.drawnLines = [];

            // 清理所有点标记
            this.pointMarkers.forEach(marker => {
                this.scene?.remove(marker);
            });
            this.pointMarkers = [];

            // 重置绘制状态
            this.pointPositions = [];
            this.currentPoint = null;

            // 清除预览线和吸附指示器
            this.clearPreviewLine();
            this.clearSnapIndicator();

            if (this.viewer) {
                this.viewer.render();
            }

        }
    }

    public clearAllLines(): void {
        this.drawnLines.forEach(line => {
            this.scene?.remove(line);
        });
        this.drawnLines = [];

        this.pointMarkers.forEach(marker => {
            this.scene?.remove(marker);
        });
        this.pointMarkers = [];

        // 清除所有方向指示器
        this.closedShapes.forEach(shape => {
            this.clearDirectionIndicator(shape);
            this.scene?.remove(shape.group);
        });
        this.closedShapes = [];

        this.hideDescriptionDialog();

        // 清除防抖定时器
        if (this.dialogCloseTimeout) {
            clearTimeout(this.dialogCloseTimeout);
            this.dialogCloseTimeout = null;
        }
        this.dialogJustClosed = false;

        // 清除预览线和吸附指示器
        this.clearPreviewLine();
        this.clearSnapIndicator();

        // 重置绘制状态
        this.pointPositions = [];
        this.currentPoint = null;

        // 重置CAD对象计数器
        this.resetCadObjectCounters();

        // 确保清理所有未完成的绘制状态
        this.clearIncompleteDrawing();

        if (this.viewer) {
            this.viewer.render();
        }

    }
    onMouseDown(event: MouseEvent): void {
        if (!this.isDrawing) return;

        if (event.button === 2) {
            this.rightMouseDown = true;
            this.rightMouseStartPos = { x: event.clientX, y: event.clientY };
            return;
        }

        if (!this.container || !this.camera) return;

        // 获取相对于容器的鼠标坐标
        const relativeCoords = this.getRelativeMouseCoords(event, this.container);
        let worldPosition = this.screenToWorld(relativeCoords.x, relativeCoords.y, this.container, this.camera);

        // 尝试吸附到DXF数据点
        const snapPoint = this.spatialGrid.findNearestPoint(worldPosition, this.snapDistance);
        if (snapPoint) {
            worldPosition = snapPoint.clone();
        }

        // 检查是否点击在DXF封闭图形内（仅在自动识别模式且开始绘制时检查）
        if (this.currentDrawingMode === DrawingMode.AUTO_DETECT && this.pointPositions.length === 0) {
            const dxfShape = this.checkPointInDxfShape(worldPosition);
            if (dxfShape) {
                this.autoDrawDxfShape(dxfShape);
                return;
            }
        }

        if (this.pointPositions.length >= 3) {
            const firstPoint = this.pointPositions[0];
            const snapDistance = 100;

            if (worldPosition.distanceTo(firstPoint) < snapDistance) {

                const lastPoint = this.pointPositions[this.pointPositions.length - 1];
                this.drawClosingLine(lastPoint, firstPoint);

                this.createClosedShape();

                this.clearPreviewLine();

                this.endDrawingMode();

                return;
            }
        }

        // 正常绘制逻辑
        if (this.pointPositions.length > 0) {
            const lastPoint = this.pointPositions[this.pointPositions.length - 1];

            this.drawPermanentLine(lastPoint, worldPosition);
        }

        this.clearPreviewLine();

        this.currentPoint = worldPosition;
        this.pointPositions.push(worldPosition);

        const geometry = new THREE.CircleGeometry(50, 32); // 调整大小
        const material = new THREE.MeshBasicMaterial({
            color: 0xff0000,
            transparent: true,
            opacity: 0.8
        });

        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.copy(worldPosition);

        mesh.renderOrder = 1001;
        mesh.position.z = 0.2;

        this.scene?.add(mesh);

        // 保存点标记引用
        this.pointMarkers.push(mesh);

        if (this.viewer) {
            this.viewer.render();
        }
    }

    /**
     * 获取相对于容器的鼠标坐标
     */
    private getRelativeMouseCoords(event: MouseEvent, container: HTMLElement): { x: number; y: number } {
        const rect = container.getBoundingClientRect();
        return {
            x: event.clientX - rect.left,
            y: event.clientY - rect.top
        };
    }

    /**
     * 将屏幕坐标转换为世界坐标 -
     */
    private screenToWorld(screenX: number, screenY: number, container: HTMLElement, camera: THREE.OrthographicCamera): THREE.Vector3 {
        // 获取相对于容器的坐标
        const relativeX = screenX;
        const relativeY = screenY;

        // 转换为标准化设备坐标 (-1 到 1)
        const mouseX = (relativeX / container.clientWidth) * 2 - 1;
        const mouseY = -(relativeY / container.clientHeight) * 2 + 1;

        // 转换为世界坐标
        const worldX = ((mouseX + 1) / 2) * (camera.right - camera.left) + camera.left + camera.position.x;
        const worldY = ((mouseY + 1) / 2) * (camera.top - camera.bottom) + camera.bottom + camera.position.y;

        return new THREE.Vector3(worldX, worldY, 0);
    }

    /**
     * 键盘事件处理
     */
    onKeyDown(event: KeyboardEvent): void {
        // Ctrl+Z 撤销
        if (event.ctrlKey && event.key === 'z') {
            event.preventDefault();
            this.undoLastAction();
        }
    }

    /**
     * 公共撤销方法
     */
    public undo(): void {
        this.undoLastAction();
    }

    /**
     * 获取所有封闭图形信息
     */
    public getClosedShapes(): Array<{
        id: string;
        description: string;
        segmentCount: number;
        segments: Array<{
            start: { x: number; y: number; z: number };
            end: { x: number; y: number; z: number };
        }>;
    }> {
        return this.closedShapes.map(shape => ({
            id: shape.id,
            description: shape.description,
            segmentCount: shape.segments.length,
            segments: shape.segments.map(segment => ({
                start: {
                    x: segment.start.x,
                    y: segment.start.y,
                    z: segment.start.z
                },
                end: {
                    x: segment.end.x,
                    y: segment.end.y,
                    z: segment.end.z
                }
            }))
        }));
    }

    /**
     * 撤销上一个操作
     */
    private undoLastAction(): void {
        if (this.pointPositions.length === 0 && this.drawnLines.length === 0) {
            console.log('没有可撤销的操作');
            return;
        }

        if (this.isDrawing && this.pointPositions.length > 0) {
            if (this.pointMarkers.length > 0) {
                const lastMarker = this.pointMarkers.pop();
                this.scene?.remove(lastMarker!);
            }

            if (this.drawnLines.length > 0) {
                const lastLine = this.drawnLines.pop();
                this.scene?.remove(lastLine!);
            }

            this.pointPositions.pop();

            if (this.pointPositions.length > 0) {
                this.currentPoint = this.pointPositions[this.pointPositions.length - 1];
            } else {
                this.currentPoint = null;
            }

            this.clearPreviewLine();

            console.log(`撤销操作完成，剩余点数: ${this.pointPositions.length}`);
        }
        else if (!this.isDrawing && this.drawnLines.length > 0) {
            const lastLine = this.drawnLines.pop();
            this.scene?.remove(lastLine!);

            if (this.pointMarkers.length > 0) {
                const lastMarker = this.pointMarkers.pop();
                this.scene?.remove(lastMarker!);
            }

            if (this.pointPositions.length > 0) {
                this.pointPositions.pop();
            }

            if (this.pointPositions.length > 0) {
                this.currentPoint = this.pointPositions[this.pointPositions.length - 1];
            } else {
                this.currentPoint = null;
            }

            console.log(`撤销线段完成，剩余线段数: ${this.drawnLines.length}`);
        }

        // 重新渲染
        if (this.viewer) {
            this.viewer.render();
        }
    }
    onMouseMove(event: MouseEvent): void {

        if (!this.isDrawing) return;

        if (!this.container || !this.camera) return;

        // 获取相对于容器的鼠标坐标
        const relativeCoords = this.getRelativeMouseCoords(event, this.container);
        let worldPosition = this.screenToWorld(relativeCoords.x, relativeCoords.y, this.container, this.camera);

        // 尝试吸附到DXF数据点
        const snapPoint = this.spatialGrid.findNearestPoint(worldPosition, this.snapDistance);
        let isSnappingToDxf = false;

        if (snapPoint) {
            worldPosition = snapPoint.clone();
            isSnappingToDxf = true;
            this.showSnapIndicator(snapPoint);
        } else {
            this.clearSnapIndicator();
        }

        // 只有在有点位置时才更新预览线
        if (this.pointPositions.length > 0) {
            this.updateLine(worldPosition, isSnappingToDxf);
        }

        if (this.viewer) {
            this.viewer.render();
        }
    }

    onMouseUp(event: MouseEvent): void {
        if (!this.isDrawing || event.button !== 2) return;

        if (this.rightMouseDown && this.rightMouseStartPos) {
            const deltaX = Math.abs(event.clientX - this.rightMouseStartPos.x);
            const deltaY = Math.abs(event.clientY - this.rightMouseStartPos.y);
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            if (distance < this.dragThreshold) {
                this.endDrawingMode();
            }
        }

        this.rightMouseDown = false;
        this.rightMouseStartPos = null;
    }

    /**
     * 更新实时预览线
     */
    private updateLine(worldPosition: THREE.Vector3, isSnappingToDxf: boolean = false): void {
        if (!this.currentPoint || this.pointPositions.length === 0) return;

        if (this.previewLine) {
            this.scene?.remove(this.previewLine);
            this.previewLine = null;
        }

        const lastPoint = this.pointPositions[this.pointPositions.length - 1];
        let targetPoint = worldPosition.clone();
        let isSnapping = isSnappingToDxf;
        let snapColor = 0x00ffff;

        // 检测首尾吸附（优先级高于DXF点吸附）
        if (this.pointPositions.length >= 3) {
            const firstPoint = this.pointPositions[0];
            const snapDistance = 100;

            if (worldPosition.distanceTo(firstPoint) < snapDistance) {
                targetPoint = firstPoint.clone();
                isSnapping = true;
                snapColor = 0x00ff00;
            }
        }

        const geometry = new THREE.BufferGeometry().setFromPoints([lastPoint, targetPoint]);

        const material = new THREE.LineBasicMaterial({
            color: isSnapping ? snapColor : 0xffff00,
            linewidth: isSnapping ? 3 : 2,
            transparent: true,
            opacity: isSnapping ? 0.9 : 0.7
        });

        this.previewLine = new THREE.Line(geometry, material);

        // 设置预览线的渲染层级
        this.previewLine.renderOrder = 1002; // 最高层级
        this.previewLine.position.z = 0.3;

        this.scene?.add(this.previewLine);
    }

    /**
     * 清除预览线
     */
    private clearPreviewLine(): void {
        if (this.previewLine) {
            this.scene?.remove(this.previewLine);
            this.previewLine = null;
            if (this.viewer) {
                this.viewer.render();
            }
        }
    }

    private drawPermanentLine(startPoint: THREE.Vector3, endPoint: THREE.Vector3): void {
        const geometry = new THREE.BufferGeometry().setFromPoints([startPoint, endPoint]);
        const material = new THREE.LineBasicMaterial({
            color: 0x0000ff,
            linewidth: 3
        });
        const visibleLine = new THREE.Line(geometry, material);

        visibleLine.renderOrder = 1000;
        visibleLine.position.z = 0.1;

        this.scene?.add(visibleLine);

        if (!this.drawnLines) {
            this.drawnLines = [];
        }
        this.drawnLines.push(visibleLine as any);
    }

    /**
     * 绘制闭合线段（与其他线段保持一致的颜色）
     */
    private drawClosingLine(startPoint: THREE.Vector3, endPoint: THREE.Vector3): void {
        const geometry = new THREE.BufferGeometry().setFromPoints([startPoint, endPoint]);
        const material = new THREE.LineBasicMaterial({
            color: 0x0000ff,
            linewidth: 3
        });
        const visibleLine = new THREE.Line(geometry, material);

        visibleLine.renderOrder = 1000;
        visibleLine.position.z = 0.1;

        this.scene?.add(visibleLine);

        if (!this.drawnLines) {
            this.drawnLines = [];
        }
        this.drawnLines.push(visibleLine as any);
    }

    /**
     * 显示DXF点吸附指示器
     */
    private showSnapIndicator(position: THREE.Vector3): void {
        this.clearSnapIndicator();

        const geometry = new THREE.RingGeometry(30, 50, 8);
        const material = new THREE.MeshBasicMaterial({
            color: 0x00ffff,
            transparent: true,
            opacity: 0.8,
            side: THREE.DoubleSide
        });

        this.snapIndicator = new THREE.Mesh(geometry, material);
        this.snapIndicator.position.copy(position);
        this.scene?.add(this.snapIndicator);
    }

    /**
     * 清除DXF点吸附指示器
     */
    private clearSnapIndicator(): void {
        if (this.snapIndicator) {
            this.scene?.remove(this.snapIndicator);
            this.snapIndicator = null;
        }
    }

    /**
     * 创建多边形几何体用于封闭区域点击检测
     */
    private createPolygonGeometry(points: THREE.Vector3[]): THREE.BufferGeometry | null {
        if (points.length < 3) return null;

        try {
            // 使用 THREE.Shape 创建多边形
            const shape = new THREE.Shape();

            // 移动到第一个点
            shape.moveTo(points[0].x, points[0].y);

            // 连接其他点
            for (let i = 1; i < points.length; i++) {
                shape.lineTo(points[i].x, points[i].y);
            }

            // 闭合路径
            shape.closePath();

            // 创建几何体
            const geometry = new THREE.ShapeGeometry(shape);
            return geometry;
        } catch (error) {
            console.warn('创建多边形几何体失败:', error);
            return null;
        }
    }

    /**
     * 创建封闭图形对象
     */
    private createClosedShape(): void {
        if (this.pointPositions.length < 3 || this.drawnLines.length === 0) {
            return;
        }

        const shapeId = `shape_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        const segments: ClosedShape['segments'] = [];

        // 创建线段数组（包括所有绘制的线段）
        for (let i = 0; i < this.drawnLines.length; i++) {
            const line = this.drawnLines[i];
            const startPoint = i === 0 ? this.pointPositions[0] : this.pointPositions[i];
            const endPoint = i === this.drawnLines.length - 1 ? this.pointPositions[0] : this.pointPositions[i + 1];

            segments.push({
                start: startPoint.clone(),
                end: endPoint.clone(),
                line: line
            });
        }

        const group = new THREE.Group();
        group.userData = { shapeId, isClosedShape: true };

        this.drawnLines.forEach(line => {
            group.add(line);
        });

        // 将所有点标记添加到组中
        this.pointMarkers.forEach(marker => {
            group.add(marker);
        });

        const fillGeometry = this.createPolygonGeometry(this.pointPositions);
        if (fillGeometry) {
            const fillMaterial = new THREE.MeshBasicMaterial({
                color: 0x0000ff,
                transparent: true,
                opacity: 0.5,
            });
            const fillMesh = new THREE.Mesh(fillGeometry, fillMaterial);
            fillMesh.userData = { isClickTarget: true, type: 'area', shapeId: shapeId };
            fillMesh.renderOrder = 1100; // 提高手动绘制图形的渲染优先级
            fillMesh.position.z = 0.2;   // 提高Z坐标，确保在分区上方
            group.add(fillMesh);
        }

        this.scene?.add(group);

        // 创建封闭图形对象
        const closedShape: ClosedShape = {
            id: shapeId,
            segments: segments,
            description: '',
            group: group,
            isSelected: false,
            currentDirectionSegmentIndex: 0, // 默认选择第一条线段
            directionReversed: false // 默认不翻转
        };

        this.closedShapes.push(closedShape);

        this.drawnLines = [];
        this.pointMarkers = [];
        this.pointPositions = [];
        this.currentPoint = null;

        // 先设置选中状态，避免在显示对话框时被清除
        this.setShapeSelected(closedShape, true);

        setTimeout(() => {
            this.showDescriptionDialog(closedShape, window.innerWidth / 2, window.innerHeight / 2);
        }, 300); // 增加延迟时间，避免与鼠标事件冲突
    }

    /**
     * 图形点击事件处理
     */
    onShapeClick(event: MouseEvent): void {
        console.log('图形点击事件触发');
        
        if (this.isDrawing) {
            console.log('当前处于绘制模式，忽略点击');
            return;
        }

        if (this.dialogJustClosed) {
            console.log('对话框刚关闭，忽略点击');
            return;
        }

        if (!this.container || !this.camera) return;

        // 获取相对于容器的鼠标坐标
        const relativeCoords = this.getRelativeMouseCoords(event, this.container);
        this.mouse.x = (relativeCoords.x / this.container.clientWidth) * 2 - 1;
        this.mouse.y = -(relativeCoords.y / this.container.clientHeight) * 2 + 1;

        this.raycaster.setFromCamera(this.mouse, this.camera);

        const clickableObjects: THREE.Object3D[] = [];

        // 收集所有可点击对象，并保存对应的图形引用
        const objectToShapeMap = new Map<THREE.Object3D, ClosedShape>();

        // 收集手动绘制的封闭图形
        this.closedShapes.forEach(shape => {
            shape.group.traverse((child) => {
                if (child.userData && child.userData.isClickTarget) {
                    clickableObjects.push(child);
                    objectToShapeMap.set(child, shape);
                }
            });
        });

        // 收集分区图形
        this.partitionGroups.forEach(partitionGroup => {
            partitionGroup.traverse((child) => {
                if (child.userData && child.userData.isClickTarget) {
                    clickableObjects.push(child);
                    // 为分区创建临时的shape对象用于点击处理
                    const tempShape: ClosedShape = {
                        id: child.userData.shapeId || 'unknown',
                        segments: [],
                        description: partitionGroup.userData.itemName || '分区',
                        group: partitionGroup,
                        isSelected: false,
                        userData: { type: 'partition' }
                    };
                    objectToShapeMap.set(child, tempShape);
                }
            });
        });

        const intersects = this.raycaster.intersectObjects(clickableObjects);

        if (intersects.length > 0) {
            // 智能选择策略：优先选择最合适的图形
            const bestIntersect = this.selectBestIntersect(intersects, objectToShapeMap);
            
            if (bestIntersect) {
                const clickedObject = bestIntersect.object;
                const userData = clickedObject.userData;

                if (userData && userData.type === 'area') {
                    const clickedShape = objectToShapeMap.get(clickedObject);
                    if (clickedShape) {
                        // 取消之前选中的图形
                        this.clearSelection();

                        // 设置当前图形为选中状态
                        this.setShapeSelected(clickedShape, true);

                        // 检查是否是分区图形
                        if (clickedShape.userData?.type === 'partition') {
                            // 为分区显示简单的信息提示
                            this.showPartitionInfo(clickedShape, event.clientX, event.clientY);
                        } else {
                            // 普通图形显示描述对话框
                            this.showDescriptionDialog(clickedShape, event.clientX, event.clientY);
                        }
                    }
                }
            }
        } else {
            // 点击空白区域，取消选中
            this.clearSelection();
        }
    }

    /**
     * 智能选择最佳的相交对象
     * 优先级：1. Z坐标最高的 2. 面积最小的 3. 距离最近的
     */
    private selectBestIntersect(intersects: THREE.Intersection[], objectToShapeMap: Map<THREE.Object3D, ClosedShape>): THREE.Intersection | null {
        if (intersects.length === 0) return null;

        // 过滤出有效的area类型对象
        const validIntersects = intersects.filter(intersect => 
            intersect.object.userData && intersect.object.userData.type === 'area'
        );

        if (validIntersects.length === 0) return null;
        if (validIntersects.length === 1) return validIntersects[0];

        // 多个对象时使用智能选择策略
        let bestIntersect = validIntersects[0];
        let bestScore = this.calculateSelectionScore(bestIntersect, objectToShapeMap);

        for (let i = 1; i < validIntersects.length; i++) {
            const currentIntersect = validIntersects[i];
            const currentScore = this.calculateSelectionScore(currentIntersect, objectToShapeMap);

            if (currentScore > bestScore) {
                bestIntersect = currentIntersect;
                bestScore = currentScore;
            }
        }

        return bestIntersect;
    }

    /**
     * 计算选择分数，分数越高越优先
     */
    private calculateSelectionScore(intersect: THREE.Intersection, objectToShapeMap: Map<THREE.Object3D, ClosedShape>): number {
        let score = 0;
        const shape = objectToShapeMap.get(intersect.object);
        
        if (!shape) return 0;

        // 1. Z坐标优先级（权重: 1000）- 越高越优先
        const zPosition = intersect.object.position.z;
        score += zPosition * 1000;

        // 2. 渲染顺序优先级（权重: 500）- 越高越优先
        if (intersect.object instanceof THREE.Mesh) {
            score += intersect.object.renderOrder * 0.5;
        }

        // 3. 面积优先级（权重: -1）- 面积越小越优先
        const area = this.calculateShapeArea(shape);
        score -= area * 0.001;

        // 4. 距离优先级（权重: -10）- 距离越近越优先
        score -= intersect.distance * 0.01;

        return score;
    }

    /**
     * 计算图形面积（简单多边形面积计算）
     */
    private calculateShapeArea(shape: ClosedShape): number {
        if (!shape.segments || shape.segments.length < 3) return 0;

        // 使用鞋带公式计算多边形面积
        let area = 0;
        const points = shape.segments.map(segment => segment.start);
        
        for (let i = 0; i < points.length; i++) {
            const j = (i + 1) % points.length;
            area += points[i].x * points[j].y;
            area -= points[j].x * points[i].y;
        }
        
        return Math.abs(area) / 2;
    }

    /**
     * 设置图形的选中状态
     */
    private setShapeSelected(shape: ClosedShape, selected: boolean): void {
        shape.isSelected = selected;

        // 如果取消选中，清理方向相关的可视化元素
        if (!selected) {
            // 清理所有粗线条
            if (shape.segments) {
                shape.segments.forEach(segment => {
                    this.removeThickDirectionLine(segment);
                });
            }
        }

        // 遍历图形组中的所有对象，改变颜色
        shape.group.traverse((child) => {
            if (child instanceof THREE.Mesh) {
                const mesh = child as THREE.Mesh;
                if (mesh.material instanceof THREE.MeshBasicMaterial) {
                    if (selected) {
                        // 选中时变为高亮色
                        mesh.material.color.setHex(0xffaa00); // 橙色高亮
                        if (mesh.userData && mesh.userData.type === 'area') {
                            mesh.material.opacity = 0.8;
                        }
                    } else {
                        // 未选中时恢复原色
                        if (mesh.userData && mesh.userData.type === 'area') {
                            mesh.material.color.setHex(0x0000ff); // 蓝色填充
                            mesh.material.opacity = 0.5;
                        } else {
                            // 点标记恢复原色
                            mesh.material.color.setHex(0xff0000); // 红色点标记
                        }
                    }
                }
            } else if (child instanceof THREE.Line) {
                const line = child as THREE.Line;
                if (line.material instanceof THREE.LineBasicMaterial) {
                    if (selected) {
                        // 选中时变为高亮色（但不是方向线条时）
                        line.material.color.setHex(0xffaa00); // 橙色高亮
                    } else {
                        // 未选中时恢复原色
                        line.material.color.setHex(0x0000ff); // 蓝色线条
                    }
                }
            }
        });

        // 重新渲染
        if (this.viewer) {
            this.viewer.render();
        }
    }

    /**
     * 设置分区图形的选中状态
     */
    private setPartitionSelected(partitionGroup: THREE.Group, selected: boolean): void {
        partitionGroup.traverse((child) => {
            if (child instanceof THREE.Mesh) {
                const mesh = child as THREE.Mesh;
                if (mesh.material instanceof THREE.MeshBasicMaterial) {
                    if (selected) {
                        // 选中时变为高亮色
                        mesh.material.color.setHex(0xffaa00); // 橙色高亮
                        mesh.material.opacity = 0.8;
                    } else {
                        // 未选中时恢复原色
                        // 从原始颜色恢复（需要保存原始颜色）
                        const originalColor = mesh.userData.originalColor || 0x0000ff;
                        mesh.material.color.setHex(originalColor);
                        mesh.material.opacity = 0.6;
                    }
                }
            } else if (child instanceof THREE.Line) {
                const line = child as THREE.Line;
                if (line.material instanceof THREE.LineBasicMaterial) {
                    if (selected) {
                        // 选中时变为高亮色
                        line.material.color.setHex(0xffaa00); // 橙色高亮
                    } else {
                        // 未选中时恢复原色
                        const originalColor = line.userData.originalColor || 0x0000ff;
                        line.material.color.setHex(originalColor);
                    }
                }
            }
        });

        // 重新渲染
        if (this.viewer) {
            this.viewer.render();
        }
    }

    /**
     * 清除所有图形的选中状态
     */
    private clearSelection(): void {
        // 清除手动绘制图形的选中状态
        this.closedShapes.forEach(shape => {
            if (shape.isSelected) {
                this.clearDirectionIndicator(shape); // 清除方向指示器
                this.setShapeSelected(shape, false);
            }
        });

        // 清除分区图形的选中状态
        this.partitionGroups.forEach(partitionGroup => {
            this.setPartitionSelected(partitionGroup, false);
        });

        // 清除分区信息提示
        const existingInfo = document.getElementById('partition-info-tooltip');
        if (existingInfo) {
            existingInfo.remove();
        }
    }

    /**
     * 显示分区信息提示
     */
    private showPartitionInfo(shape: ClosedShape, x: number, y: number): void {
        // 移除现有的信息提示
        const existingInfo = document.getElementById('partition-info-tooltip');
        if (existingInfo) {
            existingInfo.remove();
        }

        const tooltip = document.createElement('div');
        tooltip.id = 'partition-info-tooltip';
        tooltip.style.cssText = `
            position: fixed;
            left: ${x + 10}px;
            top: ${y + 10}px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10000;
            pointer-events: none;
            font-family: Arial, sans-serif;
        `;

        tooltip.textContent = `分区: ${shape.description}`;
        document.body.appendChild(tooltip);

        // 3秒后自动隐藏
        setTimeout(() => {
            if (tooltip.parentNode) {
                tooltip.parentNode.removeChild(tooltip);
            }
        }, 3000);
    }

    /**
     * 显示描述对话框 - 包含标注和方向输入
     */
    private showDescriptionDialog(shape: ClosedShape, x: number, y: number): void {
        // 只移除现有对话框DOM元素，不清除选中状态
        const existingDialog = document.getElementById('shape-description-dialog');
        if (existingDialog) {
            existingDialog.remove();
        }

        const dialog = document.createElement('div');
        dialog.id = 'shape-description-dialog';
        dialog.style.cssText = `
            position: fixed;
            left: ${x + 10}px;
            top: ${y + 10}px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
            width: 10vw;
            font-family: Arial, sans-serif;
            overflow: hidden;
        `;

        // 阻止对话框内部点击事件冒泡
        dialog.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // 标注输入区域
        const labelSection = document.createElement('div');
        labelSection.style.cssText = `
            background-color: #ffffff;
            border-radius: 6px 6px 0px 0px;
            padding: 1vh 0.8vw;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 0.8vw;
        `;

        const labelTitle = document.createElement('span');
        labelTitle.textContent = '标注';
        labelTitle.style.cssText = `
            color: rgba(13, 13, 13, 0.6);
            font-size: 1.4vh;
            font-weight: normal;
            min-width: 1.8vw;
            flex-shrink: 0;
        `;

        const labelInputWrapper = document.createElement('div');
        labelInputWrapper.style.cssText = `
            background-color: rgba(207, 239, 246, 0.2);
            border-radius: 4px;
            padding: 0.6vh 0.8vw;
            height: 2.8vh;
            display: flex;
            align-items: center;
            flex: 1;
        `;

        const labelInput = document.createElement('input');
        labelInput.type = 'text';
        labelInput.value = shape.description;
        labelInput.placeholder = '输入标注或ID';
        labelInput.style.cssText = `
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            outline: none;
            color: rgba(13, 13, 13, 0.8);
            font-size: 1.3vh;
            font-family: 'Source Han Sans CN', Arial, sans-serif;
        `;

        // 阻止输入框事件冒泡
        labelInput.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        labelInput.addEventListener('mousedown', (e) => {
            e.stopPropagation();
        });

        labelInputWrapper.appendChild(labelInput);
        labelSection.appendChild(labelTitle);
        labelSection.appendChild(labelInputWrapper);

        // 方向输入区域
        const directionSection = document.createElement('div');
        directionSection.style.cssText = `
            background-color: #ffffff;
            padding: 1vh 0.8vw;
            border-bottom: 1px solid #f0f0f0;
        `;

        // 方向标题和输入框行
        const directionRowWrapper = document.createElement('div');
        directionRowWrapper.style.cssText = `
            display: flex;
            align-items: center;
            gap: 0.8vw;
            margin-bottom: 0.8vh;
        `;

        const directionTitle = document.createElement('span');
        directionTitle.textContent = '旋转角度';
        directionTitle.style.cssText = `
            color: rgba(0, 0, 0, 0.6);
            font-size: 1.4vh;
            font-weight: normal;
            min-width: 1.8vw;
            flex-shrink: 0;
        `;

        // 方向显示框（只读，更宽）
        const directionInputWrapper = document.createElement('div');
        directionInputWrapper.style.cssText = `
            background-color: rgba(207, 239, 246, 0.2);
            border-radius: 4px;
            padding: 0.6vh 0.8vw;
            height: 2.8vh;
            display: flex;
            align-items: center;
            flex: 1;
        `;

        const directionInput = document.createElement('input');
        directionInput.type = 'text';
        directionInput.readOnly = true; // 设置为只读
        directionInput.placeholder = '0°';
        directionInput.style.cssText = `
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            outline: none;
            color: rgba(0, 0, 0, 1);
            font-size: 1.3vh;
            font-family: 'Inter', Arial, sans-serif;
            cursor: default;
            text-align: center;
        `;

        // 按钮行
        const buttonRowWrapper = document.createElement('div');
        buttonRowWrapper.style.cssText = `
            display: flex;
            align-items: center;
            gap: 0.8vw;
            padding-left: 2.6vw; /* 与标题对齐 */
        `;

        // 初始化方向线条
        if (shape.currentDirectionSegmentIndex === undefined) {
            shape.currentDirectionSegmentIndex = 0; // 默认选择第一条线段
            shape.directionReversed = false;
        }

        // 切换线条按钮
        const switchSegmentBtn = document.createElement('button');
        switchSegmentBtn.textContent = '📏 ';
        switchSegmentBtn.title = '切换方向线条';
        switchSegmentBtn.style.cssText = `
            height: 2.8vh;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2vh;
            padding: 0 0.8vw;
            color: #666;
            transition: background-color 0.2s;
            width: 30%;
        `;

        // 翻转方向按钮
        const reverseDirectionBtn = document.createElement('button');
        reverseDirectionBtn.textContent = '🔄';
        reverseDirectionBtn.title = '翻转方向';
        reverseDirectionBtn.style.cssText = `
            height: 2.8vh;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2vh;
            padding: 0 0.8vw;
            color: #666;
            transition: background-color 0.2s;
            width: 30%;
        `;

        // 按钮悬停效果
        switchSegmentBtn.addEventListener('mouseenter', () => {
            switchSegmentBtn.style.backgroundColor = '#f5f5f5';
        });
        switchSegmentBtn.addEventListener('mouseleave', () => {
            switchSegmentBtn.style.backgroundColor = 'white';
        });

        reverseDirectionBtn.addEventListener('mouseenter', () => {
            reverseDirectionBtn.style.backgroundColor = '#f5f5f5';
        });
        reverseDirectionBtn.addEventListener('mouseleave', () => {
            reverseDirectionBtn.style.backgroundColor = 'white';
        });

        // 更新方向显示
        const updateDirectionDisplay = () => {
            const angle = this.calculateSegmentDirection(shape);
            directionInput.value = angle.toFixed(1) + '°';
            this.highlightDirectionSegment(shape);
            this.updateDirectionIndicator(shape);
        };

        // 切换线条事件
        switchSegmentBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            shape.currentDirectionSegmentIndex = ((shape.currentDirectionSegmentIndex || 0) + 1) % shape.segments.length;
            updateDirectionDisplay();
        });

        // 翻转方向事件  
        reverseDirectionBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            shape.directionReversed = !shape.directionReversed;
            updateDirectionDisplay();
        });

        // 初始更新
        updateDirectionDisplay();

        // 阻止按钮事件冒泡
        switchSegmentBtn.addEventListener('mousedown', (e) => {
            e.stopPropagation();
        });
        reverseDirectionBtn.addEventListener('mousedown', (e) => {
            e.stopPropagation();
        });

        // 扩展字段输入区域（根据描述动态显示）
        const extensionSection = document.createElement('div');
        extensionSection.style.cssText = `
            background-color: #ffffff;
            padding: 1vh 0.8vw;
            border-bottom: 1px solid #f0f0f0;
            display: none;
            align-items: center;
            gap: 0.8vw;
        `;

        const extensionTitle = document.createElement('span');
        extensionTitle.textContent = '扩展';
        extensionTitle.style.cssText = `
            color: rgba(0, 0, 0, 0.6);
            font-size: 1.4vh;
            font-weight: normal;
            min-width: 1.8vw;
            flex-shrink: 0;
        `;

        const extensionInputWrapper = document.createElement('div');
        extensionInputWrapper.style.cssText = `
            background-color: rgba(207, 239, 246, 0.2);
            border-radius: 4px;
            padding: 0.6vh 0.8vw;
            height: 2.8vh;
            display: flex;
            align-items: center;
            flex: 1;
        `;

        const extensionInput = document.createElement('input');
        extensionInput.type = 'text';
        extensionInput.style.cssText = `
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            outline: none;
            color: rgba(0, 0, 0, 1);
            font-size: 1.3vh;
            font-family: 'Inter', Arial, sans-serif;
        `;

        // 阻止扩展输入框事件冒泡
        extensionInput.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        extensionInput.addEventListener('mousedown', (e) => {
            e.stopPropagation();
        });

        // 添加自动转换功能（面积输入）
        extensionInput.addEventListener('blur', (e) => {
            const value = (e.target as HTMLInputElement).value.trim();
            const placeholder = (e.target as HTMLInputElement).placeholder;
            
            // 只对面积输入进行转换
            if (placeholder.includes('面积')) {
                const converted = this.convertToSquareMillimeters(value);
                if (converted !== value) {
                    (e.target as HTMLInputElement).value = converted;
                }
            }
        });

        // 动态更新扩展字段显示
        const updateExtensionField = () => {
            const description = labelInput.value.trim();
            
            // 检查是否为房间分区
            const roomLabels = ['储物间', '配电室', '员休间', '售卖区','杂物间','VIP室'];
            const isRoom = roomLabels.some(label => description.includes(label));
            
            // 检查是否为门
            const isDoor = description.includes('门');
            
            if (isRoom) {
                // 显示面积字段
                extensionSection.style.display = 'flex';
                extensionInput.placeholder = '输入面积(mm²)';
                extensionInput.value = (shape.userData?.area || '').toString();
            } else if (isDoor) {
                // 显示人流量字段
                extensionSection.style.display = 'flex';
                extensionInput.placeholder = '输入人流量';
                extensionInput.value = (shape.userData?.crowdDensity || '').toString();
            } else {
                // 隐藏扩展字段
                extensionSection.style.display = 'none';
                extensionInput.value = '';
            }
        };

        // 初始化扩展字段显示
        updateExtensionField();

        // 监听标注输入变化，动态更新扩展字段
        labelInput.addEventListener('input', updateExtensionField);

        // 确定按钮区域
        const confirmSection = document.createElement('div');
        confirmSection.style.cssText = `
            background-color: rgba(207, 239, 246, 1);
            border-radius: 0px 0px 6px 6px;
            padding: 1.2vh;
            text-align: center;
            cursor: pointer;
            transition: background-color 0.2s;
        `;

        const confirmText = document.createElement('span');
        confirmText.textContent = '确定';
        confirmText.style.cssText = `
            color: rgba(32, 162, 160, 1);
            font-size: 1.4vh;
            font-weight: normal;
        `;

        // 事件处理
        const handleConfirm = () => {
            const inputValue = labelInput.value.trim();
            
            // 检查是否为ID输入（1-20的数字）
            const idMatch = inputValue.match(/^(\d+)$/);
            if (idMatch) {
                const id = parseInt(idMatch[1]);
                const mappedLabel = this.mapIdToLabel(id);
                if (mappedLabel) {
                    shape.description = mappedLabel;
                    console.log(`ID ${id} 映射为: ${mappedLabel}`);
                } else {
                    shape.description = inputValue;
                }
            } else {
                shape.description = inputValue;
            }
            
            // 方向已经通过calculateSegmentDirection自动计算并保存到userData中
            // 不需要手动解析directionInput.value

            // 保存扩展字段值
            const extensionValue = extensionInput.value.trim();
            if (extensionValue) {
                const description = shape.description;
                const roomLabels = ['储物间', '配电室', '员休间', '售卖区','杂物间','VIP室'];
                const isRoom = roomLabels.some(label => description.includes(label));
                const isDoor = description.includes('门');
                
                if (isRoom) {
                    if (!shape.userData) shape.userData = {};
                    shape.userData.area = parseFloat(extensionValue) || 0;
                } else if (isDoor) {
                    if (!shape.userData) shape.userData = {};
                    shape.userData.crowdDensity = parseFloat(extensionValue) || 0;
                }
            }

            // 为CAD对象分配递增ID（仅在首次创建时分配）
            if (this.isCadObject(shape.description) && shape.objectId === undefined) {
                shape.objectId = this.getNextCadObjectId(shape.description);
            }

            this.hideDescriptionDialogWithDebounce();
        };

        confirmSection.onclick = (e) => {
            e.stopPropagation();
            handleConfirm();
        };

        // 回车确认
        const handleKeyPress = (e: KeyboardEvent) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                e.stopPropagation();
                handleConfirm();
            }
        };

        labelInput.addEventListener('keypress', handleKeyPress);
        directionInput.addEventListener('keypress', handleKeyPress);
        extensionInput.addEventListener('keypress', handleKeyPress);

        // Delete键删除功能
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Delete') {
                e.preventDefault();
                e.stopPropagation();
                this.deleteSelectedShape();
            }
        };

        document.addEventListener('keydown', handleKeyDown);

        // 确认区域悬停效果
        confirmSection.addEventListener('mouseenter', () => {
            confirmSection.style.backgroundColor = 'rgba(207, 239, 246, 0.8)';
        });
        confirmSection.addEventListener('mouseleave', () => {
            confirmSection.style.backgroundColor = 'rgba(207, 239, 246, 1)';
        });

        // 点击其他地方关闭对话框
        const closeOnClickOutside = (e: MouseEvent) => {
            if (!dialog.contains(e.target as Node)) {
                this.hideDescriptionDialogWithDebounce();
                document.removeEventListener('click', closeOnClickOutside);
                document.removeEventListener('keydown', handleKeyDown);
            }
        };

        setTimeout(() => {
            document.addEventListener('click', closeOnClickOutside);
        }, 100);

        // 组装方向控件
        directionInputWrapper.appendChild(directionInput);
        directionRowWrapper.appendChild(directionTitle);
        directionRowWrapper.appendChild(directionInputWrapper);
        buttonRowWrapper.appendChild(switchSegmentBtn);
        buttonRowWrapper.appendChild(reverseDirectionBtn);
        directionSection.appendChild(directionRowWrapper);
        directionSection.appendChild(buttonRowWrapper);

        extensionInputWrapper.appendChild(extensionInput);
        extensionSection.appendChild(extensionTitle);
        extensionSection.appendChild(extensionInputWrapper);

        confirmSection.appendChild(confirmText);

        dialog.appendChild(labelSection);
        dialog.appendChild(directionSection);
        dialog.appendChild(extensionSection);
        dialog.appendChild(confirmSection);
        document.body.appendChild(dialog);

        labelInput.focus();
        labelInput.select();

        this.selectedShape = shape;
    }

    /**
     * 隐藏描述对话框
     */
    private hideDescriptionDialog(): void {
        const existingDialog = document.getElementById('shape-description-dialog');
        if (existingDialog) {
            existingDialog.remove();
        }

        // 清除方向指示器
        if (this.selectedShape) {
            this.clearDirectionIndicator(this.selectedShape);
        }

        // 清除选中状态
        this.clearSelection();
        this.selectedShape = null;
    }

    /**
     * 带防抖的隐藏描述对话框
     */
    private hideDescriptionDialogWithDebounce(): void {
        if (this.dialogCloseTimeout) {
            clearTimeout(this.dialogCloseTimeout);
        }

        this.dialogJustClosed = true;

        this.hideDescriptionDialog();

        this.dialogCloseTimeout = window.setTimeout(() => {
            this.dialogJustClosed = false;
            this.dialogCloseTimeout = null;
        }, 300);
    }

    /**
     * 删除选中的封闭图形
     */
    private deleteSelectedShape(): void {
        if (!this.selectedShape) return;

        const shapeToDelete = this.selectedShape;

        // 清除方向指示器
        this.clearDirectionIndicator(shapeToDelete);

        this.scene?.remove(shapeToDelete.group);

        const index = this.closedShapes.indexOf(shapeToDelete);
        if (index > -1) {
            this.closedShapes.splice(index, 1);
        }

        this.hideDescriptionDialogWithDebounce();

        if (this.viewer) {
            this.viewer.render();
        }

        console.log(`删除封闭图形: ${shapeToDelete.id}`);
    }

    /**
     * 公共删除方法
     */
    public deleteShape(shapeId: string): boolean {
        const shape = this.closedShapes.find(s => s.id === shapeId);
        if (shape) {
            this.selectedShape = shape;
            this.deleteSelectedShape();
            return true;
        }
        return false;
    }

    /**
     * 获取基本统计信息
     */
    public getBasicStats(): {
        totalShapes: number;
        totalSegments: number;
    } {
        let totalSegments = 0;

        this.closedShapes.forEach(shape => {
            totalSegments += shape.segments.length;
        });

        return {
            totalShapes: this.closedShapes.length,
            totalSegments
        };
    }

    /**
     * 计算封闭图形的角度 (水平向右为0度，竖直向上为90度)
     */
    private calculateShapeAngle(shape: ClosedShape): number {
        if (shape.segments.length === 0) return 0;

        // 取第一条边来计算角度
        const firstSegment = shape.segments[0];
        const dx = firstSegment.end.x - firstSegment.start.x;
        const dy = firstSegment.end.y - firstSegment.start.y;

        // 计算角度（弧度转角度）
        let angle = Math.atan2(dy, dx) * (180 / Math.PI);

        // 确保角度为正值
        if (angle < 0) {
            angle += 360;
        }

        return angle;
    }

    /**
     * 获取封闭图形的坐标点列表
     */
    private getShapeCoordinates(shape: ClosedShape): number[] {
        const coords: number[] = [];

        // 从segments中提取所有顶点坐标
        shape.segments.forEach((segment, index) => {
            // 对于第一个segment，添加起点
            if (index === 0) {
                coords.push(segment.start.x, segment.start.y);
            }
            // 添加终点
            coords.push(segment.end.x, segment.end.y);
        });

        return coords;
    }

    /**
     * 获取所有图形的边界框
     */
    private getAllShapesBounds(): number[] {
        if (this.closedShapes.length === 0) {
            return [0.0, 0.0, 0.0, 0.0];
        }

        let minX = Infinity;
        let minY = Infinity;
        let maxX = -Infinity;
        let maxY = -Infinity;

        this.closedShapes.forEach(shape => {
            shape.segments.forEach(segment => {
                // 检查起点
                minX = Math.min(minX, segment.start.x);
                minY = Math.min(minY, segment.start.y);
                maxX = Math.max(maxX, segment.start.x);
                maxY = Math.max(maxY, segment.start.y);

                // 检查终点
                minX = Math.min(minX, segment.end.x);
                minY = Math.min(minY, segment.end.y);
                maxX = Math.max(maxX, segment.end.x);
                maxY = Math.max(maxY, segment.end.y);
            });
        });

        // 返回边界框坐标 [x1, y1, x2, y2, x3, y3, x4, y4] (矩形四个顶点)
        return [minX, minY, maxX, minY, maxX, maxY, minX, maxY];
    }

    /**
     * 根据标注描述分类图形
     */
    private categorizeShapes(): {
        bounds: number[];
        cadObjects: any[];
        partitionedObjects: any[];
        layoutObjects: any[];
    } {
        const cadObjects: any[] = [];
        const partitionedObjects: any[] = [];
        const layoutObjects: any[] = [];
        let bounds: number[] = [];

        // 中文标签映射
        const cadLabels = ['门', '消防栓', '柱子', '障碍物', '墙', '玻璃墙', '分水器', '窗'];
        const partitionLabels = ['储物间', '配电室', '售卖区', '员休间', '杂物间', 'VIP室'];

        this.closedShapes.forEach(shape => {
            const description = shape.description.trim();
            // 使用计算的方向（考虑用户选择的线条索引和翻转状态）
            const direction = this.calculateSegmentDirection(shape);
            const coordinates = this.getShapeCoordinates(shape);
            const dataArray = [direction, ...coordinates];

            // 检查是否为bounds
            if (description.includes('边界') || description.includes('边框')) {
                bounds = coordinates; // bounds不需要角度，只需要坐标
                return;
            }

            // 检查是否属于CAD对象
            if (this.isMatchingChinese(description, cadLabels)) {
                this.onjectMap.set(description, this.onjectMap.get(description)! + 1);
                const cadObject: any = {
                    itemName: description,
                    data: dataArray,
                    id: this.onjectMap.get(description), // 添加递增ID
                    libraryName:"华为",
                    // 添加方向相关信息以便导入时恢复
                    directionSegmentIndex: shape.currentDirectionSegmentIndex || 0,
                    directionReversed: shape.directionReversed || false
                };

                // 如果是门，添加人流量字段
                if (description.includes('门') && shape.userData?.crowdDensity !== undefined) {
                    cadObject.crowdDensity = shape.userData.crowdDensity;
                }

                if (description.includes('墙') && shape.userData?.direction !== undefined) {
                    cadObject.data = [...coordinates]
                }
                cadObjects.push(cadObject);
            }
            // 检查是否属于分区对象
            else if (this.isMatchingChinese(description, partitionLabels)) {
                const partitionObject: any = {
                    itemName: description,
                    data: coordinates, // 分区不需要角度
                    // 添加方向相关信息以便导入时恢复
                    directionSegmentIndex: shape.currentDirectionSegmentIndex || 0,
                    directionReversed: shape.directionReversed || false
                };

                // 如果是房间分区，添加面积字段
                if (shape.userData?.area !== undefined) {
                    partitionObject.area = shape.userData.area;
                }

                partitionedObjects.push(partitionObject);
            }
            // 其他都分类为布局对象
            else {
                layoutObjects.push({
                    itemName: description || '未知对象',
                    data: dataArray,
                    parent: this.findParentRoom(shape), // 查找父级房间
                    libraryName:"华为",
                    // 添加方向相关信息以便导入时恢复
                    directionSegmentIndex: shape.currentDirectionSegmentIndex || 0,
                    directionReversed: shape.directionReversed || false
                });
            }
        });

        // 如果没有专门的bounds图形，使用所有图形的边界框
        if (bounds.length === 0) {
            bounds = this.getAllShapesBounds();
        }

        return { bounds, cadObjects, partitionedObjects, layoutObjects };
    }

    /**
     * 查找布局对象的父级房间
     */
    private findParentRoom(shape: ClosedShape): string {
        // 获取图形的中心点
        const center = this.getShapeCenter(shape);
        
        // 遍历所有分区，检查该点是否在分区内
        for (const partitionGroup of this.partitionGroups) {
            if (this.isPointInPartition(center, partitionGroup)) {
                return partitionGroup.userData.itemName || '售卖区';
            }
        }
        
        // 如果没有找到分区，检查手动绘制的分区房间
        for (const closedShape of this.closedShapes) {
            if (this.isRoomShape(closedShape) && this.isPointInShape(center, closedShape)) {
                return closedShape.description || '售卖区';
            }
        }
        
        // 默认返回售卖区
        return '售卖区';
    }

    /**
     * 获取图形中心点
     */
    private getShapeCenter(shape: ClosedShape): THREE.Vector3 {
        let sumX = 0, sumY = 0, count = 0;
        
        shape.segments.forEach(segment => {
            sumX += segment.start.x;
            sumY += segment.start.y;
            count++;
        });
        
        return new THREE.Vector3(sumX / count, sumY / count, 0);
    }

    /**
     * 检查点是否在分区内
     */
    private isPointInPartition(point: THREE.Vector3, partitionGroup: THREE.Group): boolean {
        // 简单的点在多边形内检测（射线法）
        // 这里可以根据需要实现更精确的算法
        return false; // 暂时返回false，需要根据实际情况实现
    }

    /**
     * 检查点是否在图形内
     */
    private isPointInShape(point: THREE.Vector3, shape: ClosedShape): boolean {
        // 简单的点在多边形内检测
        return false; // 暂时返回false，需要根据实际情况实现
    }

    /**
     * 判断是否为房间图形
     */
    private isRoomShape(shape: ClosedShape): boolean {
        const description = shape.description.toLowerCase();
        const roomKeywords = ['room', '房间', '区域', 'area', 'zone'];
        return roomKeywords.some(keyword => description.includes(keyword));
    }



    /**
     * 导出训练数据标注格式
     */
    public exportTrainingData(storeName: string = "华为-0001号店铺"): string {
        // 验证全局属性是否已填写
        if (!this.globalHeight.trim() || !this.globalArea.trim()) {
            ElMessage.warning('请先填写全局层高和面积信息');
            throw new Error('全局属性未完整填写');
        }

        const { bounds, cadObjects, partitionedObjects, layoutObjects } = this.categorizeShapes();
        
        const trainingData = {
            "properties": {
                "uuid": new Date().getTime(),
                "name": storeName,
                "modelLibraries": ["华为"]
            },
            "CAD": [
                {
                    "itemName": "边界",
                    "data": bounds
                },
                {
                    "itemName": "底图实物",
                    "data": cadObjects
                },
                {
                    "itemName": "层高",
                    "data": parseFloat(this.globalHeight) || 0
                },
                {
                    "itemName": "面积",
                    "data": parseFloat(this.globalArea) || 0
                }
            ],
            "rooms": partitionedObjects,
            "layout_objects": layoutObjects
        };

        this.onjectMap.clear();
        this.initOnjectMap();
        return JSON.stringify(trainingData, null, 2);
    }

    /**
     * 导出训练数据并下载为JSON文件
     */
    public downloadTrainingData(storeName: string = "华为-0001号店铺", filename: string = "training_data.json"): void {
        try {
            const jsonData = this.exportTrainingData(storeName);

            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            URL.revokeObjectURL(url);

            console.log('训练数据已导出:', jsonData);
        } catch (error) {
            console.error('导出失败:', error);
            // 如果是验证错误，不再重复提示
            if (!(error as Error).message.includes('全局属性未完整填写')) {
                ElMessage.error('导出训练数据失败');
            }
        }
    }

    /**
     * 获取所有可识别的标签信息（已删除英文映射）
     */
    public getRecognizableLabels(): Array<{ chinese: string; category: string }> {
        return this.getRecognizableLabelsWithId().map(item => ({
            chinese: item.chinese,
            category: item.category
        }));
    }

    /**
     * 获取带ID的标签信息
     */
    public getRecognizableLabelsWithId(): Array<{ id: number; chinese: string; category: string }> {
        return [
            // 边界和结构 (1-2)
            { id: 0, chinese: '边界', category: '边界结构' },
            { id: 101, chinese: '墙', category: 'CAD对象' },
            { id: 1, chinese: '门', category: 'CAD对象' },
            { id: 2, chinese: '消防栓', category: 'CAD对象'},
            { id: 3, chinese: '柱子', category: 'CAD对象' },
            { id: 4, chinese: '障碍物', category: 'CAD对象' },
            { id: 107, chinese: '玻璃墙', category: 'CAD对象' },
            { id: 108, chinese: '分水器', category: 'CAD对象' },
            { id: 109, chinese: '窗', category: 'CAD对象' },
            
            
            // 房间分区 (3-6)
            { id: 5, chinese: '储物间', category: '房间分区' },
            { id: 6, chinese: '配电室', category: '房间分区' },
            { id: 7, chinese: '员休间', category: '房间分区' },
            { id: 8, chinese: '售卖区', category: '房间分区' },
            { id: 105, chinese: '杂物间', category: '房间分区' },
            { id: 106, chinese: 'VIP室', category: '房间分区' },
            
            // 收银台系列 
            { id: 102, chinese: '弱电箱', category: '布局对象' },
            { id: 103, chinese: '强电箱', category: '布局对象' },
            { id: 104, chinese: '一桌三椅', category: '布局对象' },
            { id: 9, chinese: '900收银台', category: '布局对象' },
            { id: 10, chinese: '180收银台', category: '布局对象' },
            
            { id: 11, chinese: '2000教育桌', category: '布局对象' },
            { id: 12, chinese: '2000体验台', category: '布局对象' },
            { id: 13, chinese: '3000教育桌', category: '布局对象' },
            { id: 14, chinese: '3000体验台', category: '布局对象' },
            
            { id: 15, chinese: 'L2400-H950灯箱', category: '布局对象' },
            { id: 16, chinese: 'L2400-H1150灯箱', category: '布局对象' },
            { id: 17, chinese: 'L3600-H950灯箱', category: '布局对象' },
            { id: 18, chinese: 'L3600-H1150灯箱', category: '布局对象' },
            { id: 19, chinese: 'L4800-H950灯箱', category: '布局对象' },
            { id: 20, chinese: 'L4800-H1150灯箱', category: '布局对象' },
            { id: 21, chinese: 'L6000-H950灯箱', category: '布局对象' },
            { id: 22, chinese: 'L6000-H1150灯箱', category: '布局对象' },
            { id: 23, chinese: 'L7200-H950灯箱', category: '布局对象' },
            { id: 24, chinese: 'L7200-H1150灯箱', category: '布局对象' },
            
            { id: 25, chinese: '电子水牌', category: '布局对象' },
            { id: 26, chinese: '授权牌', category: '布局对象' },
            { id: 27, chinese: '条凳', category: '布局对象' },
            
            // 运动健康系列 (按尺寸排序)
            { id: 28, chinese: '运动健康L2400-H2400', category: '布局对象' },
            { id: 29, chinese: '运动健康L2400-H2800', category: '布局对象' },
            { id: 30, chinese: '运动健康L2400-H3000', category: '布局对象' },
            { id: 31, chinese: '运动健康L3600-H2800', category: '布局对象' },
            { id: 32, chinese: '运动健康L3600-H3000', category: '布局对象' },
            { id: 33, chinese: '运动健康BL2400-H2400', category: '布局对象' },
            { id: 34, chinese: '运动健康BL2400-H2800', category: '布局对象' },
            { id: 35, chinese: '运动健康BL2400-H3000', category: '布局对象' },
            { id: 36, chinese: '运动健康BL3600-H2800', category: '布局对象' },
            { id: 37, chinese: '运动健康BL3600-H3000', category: '布局对象' },
            
            // 影音娱乐系列
            { id: 38, chinese: '影音娱乐L2400-H2400', category: '布局对象' },
            { id: 39, chinese: '影音娱乐L2400-H2800', category: '布局对象' },
            { id: 40, chinese: '影音娱乐L2400-H3000', category: '布局对象' },
            { id: 41, chinese: '影音娱乐L3600-H2800', category: '布局对象' },
            { id: 42, chinese: '影音娱乐L3600-H3000', category: '布局对象' },
            { id: 43, chinese: '影音娱乐BL2400-H2400', category: '布局对象' },
            { id: 44, chinese: '影音娱乐BL2400-H2800', category: '布局对象' },
            { id: 45, chinese: '影音娱乐BL2400-H3000', category: '布局对象' },
            { id: 46, chinese: '影音娱乐BL3600-H2800', category: '布局对象' },
            { id: 47, chinese: '影音娱乐BL3600-H3000', category: '布局对象' },
            
            // 智慧办公系列
            { id: 48, chinese: '智慧办公L2400-H2400', category: '布局对象' },
            { id: 49, chinese: '智慧办公L2400-H2800', category: '布局对象' },
            { id: 50, chinese: '智慧办公L2400-H3000', category: '布局对象' },
            { id: 51, chinese: '智慧办公L3600-H2800', category: '布局对象' },
            { id: 52, chinese: '智慧办公L3600-H3000', category: '布局对象' },
            { id: 53, chinese: '智慧办公BL2400-H2400', category: '布局对象' },
            // 智慧屏系列
            { id: 54, chinese: '智慧屏L2400-H2800', category: '布局对象' },
            { id: 55, chinese: '智慧屏L2400-H3000', category: '布局对象' },
            { id: 56, chinese: '智慧屏L3600-H2800', category: '布局对象' },
            { id: 57, chinese: '智慧屏L3600-H3000', category: '布局对象' },
            { id: 58, chinese: '智慧屏L4800-H2800', category: '布局对象' },
            { id: 59, chinese: '智慧屏L4800-H3000', category: '布局对象' },
            { id: 60, chinese: '智慧屏L6000-H2800', category: '布局对象' },
            { id: 61, chinese: '智慧屏L6000-H3000', category: '布局对象' },
            { id: 62, chinese: '智慧屏L7200-H2800', category: '布局对象' },
            { id: 63, chinese: '智慧屏L7200-H3000', category: '布局对象' },
            { id: 64, chinese: '智慧屏L8400-H2800', category: '布局对象' },
            { id: 65, chinese: '智慧屏L8400-H3000', category: '布局对象' },
    
            // 家居系列

            { id: 66, chinese: '智能家居L2400-H2800', category: '布局对象' },
            { id: 67, chinese: '智能家居L2400-H3000', category: '布局对象' },
            { id: 68, chinese: '智能家居L3600-H2800', category: '布局对象' },
            { id: 69, chinese: '智能家居L3600-H3000', category: '布局对象' },
    
            // 配件系列
            { id: 70, chinese: '综合配件L2400-H2400', category: '布局对象' },
            { id: 71, chinese: '综合配件L2400-H2800', category: '布局对象' },
            { id: 72, chinese: '综合配件L2400-H3000', category: '布局对象' },
            { id: 73, chinese: '综合配件L3600-H2800', category: '布局对象' },
            { id: 74, chinese: '综合配件L3600-H3000', category: '布局对象' }
        ];
    }

    /**
     * 根据ID映射到对应的中文标签
     */
    private mapIdToLabel(id: number): string | null {
        const labelMap = this.getRecognizableLabelsWithId();
        const found = labelMap.find(item => item.id === id);
        return found ? found.chinese : null;
    }

    /**
     * 绑定快捷键
     */
    public bindHotkeys(): void {
        // 如果已经绑定过，先移除
        if (this.hotkeyHandler) {
            document.removeEventListener('keydown', this.hotkeyHandler);
            this.hotkeyHandler = null;
        }

        // 防抖状态
        let isProcessingHotkey = false;

        this.hotkeyHandler = (event: KeyboardEvent) => {
            // 防止在输入框中触发快捷键
            if (event.target && ['INPUT', 'TEXTAREA'].includes((event.target as Element).tagName)) {
                return;
            }

            // 防抖处理
            if (isProcessingHotkey) {
                return;
            }
            isProcessingHotkey = true;
            
            // 200ms后重置防抖状态
            setTimeout(() => {
                isProcessingHotkey = false;
            }, 200);

            switch (event.key.toLowerCase()) {
                case 'a':
                    event.preventDefault();
                    this.startDrawingMode();
                    console.log(' 按键A：开始绘制模式');
                    break;
                case 'm':
                    event.preventDefault();
                    this.toggleDrawingMode();
                    console.log(' 按键M：切换绘制模式');
                    break;
                case 'x':
                    event.preventDefault();
                    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                    this.downloadTrainingData("华为-0001号店铺", `training_data_${timestamp}.json`);
                    console.log(' 按键X：下载训练数据');
                    break;
                case 'l':
                    event.preventDefault();
                    this.loadTrainingDataFromFile();
                    console.log(' 按键L：加载训练数据文件');
                    break;
                case 's':
                    event.preventDefault();
                    this.showDxfShapeStats();
                    console.log(' 按键S：显示图形统计信息');
                    break;
                case 'escape':
                    event.preventDefault();
                    if (this.isDrawing) {
                        this.endDrawingMode();
                        console.log(' ESC：结束绘制模式');
                    }
                    break;
            }
        };

        // 添加新的监听器
        document.addEventListener('keydown', this.hotkeyHandler);
        
        console.log(' 快捷键已绑定：A-开始绘制, M-切换模式, X-下载训练数据, L-加载训练数据, S-显示统计, ESC-结束绘制');
    }

    /**
     * 移除快捷键绑定
     */
    public unbindHotkeys(): void {
        if (this.hotkeyHandler) {
            document.removeEventListener('keydown', this.hotkeyHandler);
            this.hotkeyHandler = null;
            console.log(' 快捷键已移除');
        }
    }

    /**
     * 清理资源
     */
    public dispose(): void {
        // 移除事件监听器
        this.unbindHotkeys();
        
        // 清理绘制状态
        this.clearAllLines();
        
        // 清理全局输入容器
        if (this.globalInputContainer) {
            this.globalInputContainer.remove();
            this.globalInputContainer = null;
        }
        
        // 重置状态
        this.isDrawing = false;
        this.currentDrawingMode = DrawingMode.MANUAL;
        this.modeToggleDebounce = false;
        this.globalHeight = '';
        this.globalArea = '';
        
        // 重置CAD对象计数器
        this.resetCadObjectCounters();
        
        console.log('🧹 FreeDrawManager 资源已清理');
    }

    private guid() {
        function S4() {
            return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
        }
        return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
    }

    /**
     * 检查描述是否匹配中文标签
     */
    private isMatchingChinese(description: string, allowedLabels: string[]): boolean {
        // 检查中文标签
        for (const label of allowedLabels) {
            if (description.includes(label)) {
                return true;
            }
        }

        return false;
    }



    /**
     * 绘制分区数据
     * @param partitionData 分区数据数组
     * @param schemeIndex 要绘制的方案索引，默认为0
     */
    public drawPartitions(partitionData: any[][], schemeIndex: number = 0): void {
        if (!this.scene) {
            console.error('Scene not available for drawing partitions');
            return;
        }

        if (schemeIndex >= partitionData.length) {
            console.error(`Invalid scheme index: ${schemeIndex}. Available schemes: ${partitionData.length}`);
            return;
        }

        // 清除之前的分区绘制
        this.clearPartitions();

        const scheme = partitionData[schemeIndex];
        console.log(`开始绘制分区方案 ${schemeIndex + 1}，包含 ${scheme.length} 个房间`);

        scheme.forEach((room, roomIndex) => {
            this.drawSinglePartition(room, roomIndex);
        });

        // 确保点击监听器存在（用于分区点击选择）
        this.ensureShapeClickListener();

        // 重新渲染
        if (this.viewer) {
            this.viewer.render();
        }
    }

    /**
     * 绘制单个分区房间
     * @param room 房间数据对象
     * @param colorIndex 颜色索引
     */
    private drawSinglePartition(room: any, colorIndex: number): void {
        if (!room.itemName || !room.data || !Array.isArray(room.data)) {
            console.warn('Invalid room data:', room);
            return;
        }

        const { itemName, data } = room;
        
        // 确保数据点数量是偶数（x,y坐标对）
        if (data.length % 2 !== 0) {
            console.warn(`Invalid coordinate data for room ${itemName}:`, data);
            return;
        }

        // 将坐标数组转换为THREE.Vector3点数组
        const points: THREE.Vector3[] = [];
        for (let i = 0; i < data.length; i += 2) {
            points.push(new THREE.Vector3(data[i], data[i + 1], 0));
        }

        if (points.length < 3) {
            console.warn(`Insufficient points for room ${itemName}:`, points.length);
            return;
        }

        // 选择颜色
        const color = this.partitionColors[colorIndex % this.partitionColors.length];

        // 创建房间组
        const roomGroup = new THREE.Group();
        roomGroup.userData = { 
            type: 'partition', 
            itemName: itemName,
            isPartitionRoom: true 
        };

        // 创建线框
        this.createPartitionOutline(points, roomGroup, color);

        // 创建填充区域
        this.createPartitionFill(points, roomGroup, color);

        // 添加到场景
        this.scene?.add(roomGroup);
        this.partitionGroups.push(roomGroup);

        console.log(`已绘制房间: ${itemName}，颜色索引: ${colorIndex}`);
    }

    /**
     * 创建分区轮廓线
     * @param points 顶点数组
     * @param group 父组
     * @param color 颜色
     */
    private createPartitionOutline(points: THREE.Vector3[], group: THREE.Group, color: number): void {
        // 确保路径闭合，添加第一个点到末尾
        const closedPoints = [...points];
        if (!points[0].equals(points[points.length - 1])) {
            closedPoints.push(points[0].clone());
        }

        const geometry = new THREE.BufferGeometry().setFromPoints(closedPoints);
        const material = new THREE.LineBasicMaterial({
            color: color,
            linewidth: 2,
            transparent: true,
            opacity: 0.8
        });

        const line = new THREE.Line(geometry, material);
        line.renderOrder = 1000;
        line.position.z = 0.1;
        line.userData.originalColor = color; // 保存原始颜色
        
        group.add(line);
    }

    /**
     * 创建分区填充区域
     * @param points 顶点数组
     * @param group 父组
     * @param color 颜色
     */
    private createPartitionFill(points: THREE.Vector3[], group: THREE.Group, color: number): void {
        try {
            // 使用 THREE.Shape 创建多边形填充
            const shape = new THREE.Shape();
            
            // 移动到第一个点
            shape.moveTo(points[0].x, points[0].y);
            
            // 连接其他点
            for (let i = 1; i < points.length; i++) {
                shape.lineTo(points[i].x, points[i].y);
            }
            
            // 闭合路径
            shape.closePath();
            
            // 创建几何体
            const geometry = new THREE.ShapeGeometry(shape);
            const material = new THREE.MeshBasicMaterial({
                color: color,
                transparent: true,
                opacity: 0.6,
                side: THREE.DoubleSide
            });
            
            const mesh = new THREE.Mesh(geometry, material);
            mesh.userData = { 
                isClickTarget: true, 
                type: 'area', 
                shapeId: `partition_${group.userData.itemName}_${Date.now()}`,
                originalColor: color // 保存原始颜色
            };
            mesh.renderOrder = 900; // 分区的渲染优先级低于手动绘制的图形
            mesh.position.z = 0.05; // 分区在较低的Z层
            
            group.add(mesh);
        } catch (error) {
            console.warn('创建分区填充失败:', error);
        }
    }

    /**
     * 清除所有分区绘制
     */
    public clearPartitions(): void {
        this.partitionGroups.forEach(group => {
            this.scene?.remove(group);
        });
        this.partitionGroups = [];

        // 重新渲染
        if (this.viewer) {
            this.viewer.render();
        }

    }

    /**
     * 切换分区方案
     * @param partitionData 分区数据数组
     * @param schemeIndex 方案索引
     */
    public switchPartitionScheme(partitionData: any[][], schemeIndex: number): void {
        console.log(`切换到分区方案 ${schemeIndex + 1}`);
        this.drawPartitions(partitionData, schemeIndex);
        // drawPartitions 方法内部已经调用了 ensureShapeClickListener()
    }

    /**
     * 获取可用的分区方案数量
     * @param partitionData 分区数据数组
     * @returns 方案数量
     */
    public getPartitionSchemeCount(partitionData: any[][]): number {
        return partitionData.length;
    }

    /**
     * 隐藏所有分区
     */
    public hidePartitions(): void {
        this.partitionGroups.forEach(group => {
            group.visible = false;
        });
        
        // 重新渲染场景
        if (this.viewer) {
            this.viewer.render();
        }
        
        console.log('🙈 分区已隐藏');
    }

    /**
     * 显示所有分区
     */
    public showPartitions(): void {
        this.partitionGroups.forEach(group => {
            group.visible = true;
        });
        
        // 重新渲染场景
        if (this.viewer) {
            this.viewer.render();
        }
        
        console.log('👀 分区已显示');
    }

    /**
     * 隐藏所有手动绘制的图形
     */
    public hideShapes(): void {
        this.closedShapes.forEach(closedShape => {
            closedShape.group.visible = false;
        });
        
        // 重新渲染场景
        if (this.viewer) {
            this.viewer.render();
        }
        
        console.log('🙈 手动绘制图形已隐藏');
    }

    /**
     * 显示所有手动绘制的图形
     */
    public showShapes(): void {
        this.closedShapes.forEach(closedShape => {
            closedShape.group.visible = true;
        });
        
        // 重新渲染场景
        if (this.viewer) {
            this.viewer.render();
        }
        
        console.log('👀 手动绘制图形已显示');
    }

    /**
     * 切换分区显示状态
     */
    public togglePartitions(): void {
        const anyVisible = this.partitionGroups.some(group => group.visible);
        if (anyVisible) {
            this.hidePartitions();
        } else {
            this.showPartitions();
        }
    }

    /**
     * 切换图形显示状态
     */
    public toggleShapes(): void {
        const anyVisible = this.closedShapes.some(shape => shape.group.visible);
        if (anyVisible) {
            this.hideShapes();
        } else {
            this.showShapes();
        }
    }

    /**
     * 获取分区显示状态
     */
    public getPartitionsVisibility(): boolean {
        return this.partitionGroups.length > 0 && this.partitionGroups.some(group => group.visible);
    }

    /**
     * 获取图形显示状态
     */
    public getShapesVisibility(): boolean {
        return this.closedShapes.length > 0 && this.closedShapes.some(shape => shape.group.visible);
    }
    /**
     * 获取当前绘制的分区信息
     */
    public getCurrentPartitionInfo(): Array<{
        itemName: string;
        pointCount: number;
    }> {
        const info: Array<{ itemName: string; pointCount: number }> = [];
        
        this.partitionGroups.forEach(group => {
            if (group.userData && group.userData.isPartitionRoom) {
                // 计算点数量（从线条几何体中获取）
                let pointCount = 0;
                group.traverse((child) => {
                    if (child instanceof THREE.Line) {
                        const geometry = child.geometry as THREE.BufferGeometry;
                        pointCount = geometry.attributes.position.count - 1; // 减去闭合点
                    }
                });
                
                info.push({
                    itemName: group.userData.itemName,
                    pointCount: pointCount
                });
            }
        });
        
        return info;
    }

    /**
     * 从文件加载训练数据
     */
    public loadTrainingDataFromFile(): void {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.style.display = 'none';
        
        input.onchange = (event: Event) => {
            const file = (event.target as HTMLInputElement).files?.[0];
            if (file) {
                this.loadTrainingDataFromFileObject(file);
            }
            document.body.removeChild(input);
        };
        
        document.body.appendChild(input);
        input.click();
    }

    /**
     * 从文件对象加载训练数据
     */
    public loadTrainingDataFromFileObject(file: File): void {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const jsonData = JSON.parse(e.target?.result as string);
                this.loadTrainingData(jsonData);
                // 显示加载信息
                if (jsonData.properties) {
                    console.log(` 文件信息:`, {
                        name: jsonData.properties.name || '未知',
                    });
                }
            } catch (error: any) {
                console.error(' JSON文件解析失败:', error);
            }
        };
        reader.onerror = () => {
            console.error(' 文件读取失败');
        };
        reader.readAsText(file);
    }

    /**
     * 加载训练数据并重新绘制
     */
    public loadTrainingData(trainingData: any): void {
        if (!this.scene) {
            console.error('Scene not available for loading training data');
            return;
        }

        // 清除现有的所有绘制内容
        this.clearAllLines();
        this.clearPartitions();

        try {
            // 解析并重新创建图形
            this.parseAndCreateShapes(trainingData);
            
            // 重新渲染
            if (this.viewer) {
                this.viewer.render();
            }

        } catch (error) {
            console.error('训练数据加载失败:', error);
        }
    }

    /**
     * 解析训练数据并创建图形
     */
    private parseAndCreateShapes(trainingData: any): void {
        // 重置CAD对象计数器
        this.resetCadObjectCounters();

        // 1. 处理CAD对象（边界和底图实物）
        if (trainingData.CAD && Array.isArray(trainingData.CAD)) {
            trainingData.CAD.forEach((cadItem: any) => {
                if (cadItem.itemName === '底图实物' && cadItem.data && Array.isArray(cadItem.data)) {
                    // 处理CAD对象数组
                    cadItem.data.forEach((obj: any) => {
                        const objectId = typeof obj.id === 'number' ? obj.id : undefined;
                        const directionSegmentIndex = typeof obj.directionSegmentIndex === 'number' ? obj.directionSegmentIndex : 0;
                        const directionReversed = typeof obj.directionReversed === 'boolean' ? obj.directionReversed : false;

                        if(obj.itemName === '墙' || obj.itemName === '玻璃墙'){
                            this.createShapeFromData(obj.itemName, obj.data, false, undefined, obj.crowdDensity, objectId, directionSegmentIndex, directionReversed);
                        }else{
                            this.createShapeFromData(obj.itemName, obj.data, true, undefined, obj.crowdDensity, objectId, directionSegmentIndex, directionReversed);
                        }
                        // 更新计数器以保持ID的连续性
                        if (objectId && this.isCadObject(obj.itemName)) {
                            const currentCount = this.cadObjectCounters.get(obj.itemName) || 0;
                            if (objectId > currentCount) {
                                this.cadObjectCounters.set(obj.itemName, objectId);
                            }
                        }
                    });
                } else if (cadItem.itemName === '边界' && cadItem.data) {
                    // 处理边界数据
                    this.createShapeFromData('边界', cadItem.data, false);
                } else if (cadItem.itemName === '层高' && cadItem.data) {
                    this.globalHeight = cadItem.data.toString();
                } else if (cadItem.itemName === '面积' && cadItem.data) {
                    this.globalArea = cadItem.data.toString();
                }
            });
        }

        // 2. 处理房间分区
        if (trainingData.rooms && Array.isArray(trainingData.rooms)) {
            trainingData.rooms.forEach((room: any) => {
                const objectId = typeof room.id === 'number' ? room.id : undefined;
                const directionSegmentIndex = typeof room.directionSegmentIndex === 'number' ? room.directionSegmentIndex : 0;
                const directionReversed = typeof room.directionReversed === 'boolean' ? room.directionReversed : false;
                this.createShapeFromData(room.itemName, room.data, false, room.area, undefined, objectId, directionSegmentIndex, directionReversed);
            });
        }

        // 3. 处理布局对象
        if (trainingData.layout_objects && Array.isArray(trainingData.layout_objects)) {
            trainingData.layout_objects.forEach((obj: any) => {
                const objectId = typeof obj.id === 'number' ? obj.id : undefined;
                const directionSegmentIndex = typeof obj.directionSegmentIndex === 'number' ? obj.directionSegmentIndex : 0;
                const directionReversed = typeof obj.directionReversed === 'boolean' ? obj.directionReversed : false;
                this.createShapeFromData(obj.itemName, obj.data, true, undefined, undefined, objectId, directionSegmentIndex, directionReversed);
            });
        }

        // 4. 加载全局属性
        if (trainingData.properties) {
            if (trainingData.properties.height !== undefined) {
                this.globalHeight = trainingData.properties.height.toString();
            }
            if (trainingData.properties.area !== undefined) {
                this.globalArea = trainingData.properties.area.toString();
            }
            // 更新全局输入UI
            this.updateGlobalInputValues();
        }
    }

    /**
     * 从数据创建图形
     * @param itemName 物品名称
     * @param data 坐标数据数组
     * @param hasDirection 是否包含方向信息（第一个元素是方向）
     * @param area 房间面积（可选）
     * @param crowdDensity 人流量（可选）
     * @param objectId 对象ID（可选）
     * @param directionSegmentIndex 方向线条索引（可选）
     * @param directionReversed 方向是否翻转（可选）
     */
    private createShapeFromData(itemName: string, data: number[], hasDirection: boolean, area?: number, crowdDensity?: number, objectId?: number, directionSegmentIndex?: number, directionReversed?: boolean): void {
        if (!data || data.length < 6) { // 至少需要3个点（6个坐标值）
            console.warn(`数据不足，无法创建图形: ${itemName}`, data);
            return;
        }

        let coordinates = data;
        let direction = 0;

        // 如果包含方向信息，提取方向并获取坐标
        if (hasDirection && data.length > 2) {
            direction = data[0];
            coordinates = data.slice(1); // 从第二个元素开始是坐标
        }

        // 确保坐标数量是偶数
        if (coordinates.length % 2 !== 0) {
            console.warn(`坐标数量不是偶数，无法创建图形: ${itemName}`, coordinates);
            return;
        }

        // 将坐标数组转换为THREE.Vector3点数组
        const points: THREE.Vector3[] = [];
        for (let i = 0; i < coordinates.length; i += 2) {
            points.push(new THREE.Vector3(coordinates[i], coordinates[i + 1], 0));
        }

        if (points.length < 3) {
            console.warn(`点数不足，无法创建图形: ${itemName}`, points.length);
            return;
        }

        // 创建封闭图形
        this.createShapeFromPoints(points, itemName, direction, area, crowdDensity, objectId, directionSegmentIndex, directionReversed);
    }

    /**
     * 从点数组创建封闭图形
     * @param points 点数组
     * @param description 图形描述
     * @param direction 方向角度
     * @param area 房间面积（可选）
     * @param crowdDensity 人流量（可选）
     * @param objectId 对象ID（可选）
     * @param directionSegmentIndex 方向线条索引（可选）
     * @param directionReversed 方向是否翻转（可选）
     */
    private createShapeFromPoints(points: THREE.Vector3[], description: string, direction: number = 0, area?: number, crowdDensity?: number, objectId?: number, directionSegmentIndex?: number, directionReversed?: boolean): void {
        if (points.length < 3) return;

        const shapeId = `shape_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        const segments: ClosedShape['segments'] = [];
        const lines: (THREE.Line | THREE.Mesh)[] = [];
        const markers: THREE.Mesh[] = [];

        // 创建线段
        for (let i = 0; i < points.length; i++) {
            const startPoint = points[i];
            const endPoint = points[(i + 1) % points.length]; // 自动闭合

            // 创建线段几何体和材质
            const geometry = new THREE.BufferGeometry().setFromPoints([startPoint, endPoint]);
            const material = new THREE.LineBasicMaterial({
                color: 0x0000ff,
                linewidth: 3
            });
            const line = new THREE.Line(geometry, material);
            line.renderOrder = 1000;
            line.position.z = 0.1;

            this.scene?.add(line);
            lines.push(line);

            // 添加到segments数组
            segments.push({
                start: startPoint.clone(),
                end: endPoint.clone(),
                line: line
            });
        }

        // 创建点标记
        points.forEach(point => {
            const geometry = new THREE.CircleGeometry(50, 32);
            const material = new THREE.MeshBasicMaterial({
                color: 0xff0000,
                transparent: true,
                opacity: 0.8
            });
            const mesh = new THREE.Mesh(geometry, material);
            mesh.position.copy(point);
            mesh.renderOrder = 1001;
            mesh.position.z = 0.2;

            this.scene?.add(mesh);
            markers.push(mesh);
        });

        // 创建组
        const group = new THREE.Group();
        group.userData = { shapeId, isClosedShape: true };

        // 将线段和点标记添加到组
        lines.forEach(line => group.add(line));
        markers.forEach(marker => group.add(marker));

        // 创建填充区域
        const fillGeometry = this.createPolygonGeometry(points);
        if (fillGeometry) {
            const fillMaterial = new THREE.MeshBasicMaterial({
                color: 0x0000ff,
                transparent: true,
                opacity: 0.5,
            });
            const fillMesh = new THREE.Mesh(fillGeometry, fillMaterial);
            fillMesh.userData = { isClickTarget: true, type: 'area', shapeId: shapeId };
            fillMesh.renderOrder = 1100;
            fillMesh.position.z = 0.2;
            group.add(fillMesh);
        }

        this.scene?.add(group);

        // 创建封闭图形对象
        const closedShape: ClosedShape = {
            id: shapeId,
            objectId: objectId,
            segments: segments,
            description: description,
            group: group,
            isSelected: false,
            currentDirectionSegmentIndex: directionSegmentIndex !== undefined ? directionSegmentIndex : 0, // 使用传入的索引或默认为0
            directionReversed: directionReversed !== undefined ? directionReversed : false, // 使用传入的翻转状态或默认为false
            userData: {
                direction: direction,
                area: area,
                crowdDensity: crowdDensity
            }
        };

        this.closedShapes.push(closedShape);

        console.log(`已重新创建图形: ${description}, 方向: ${direction}°, 线条索引: ${closedShape.currentDirectionSegmentIndex}, 翻转: ${closedShape.directionReversed}`);
    }

    /**
     * 切换到手动绘制模式
     */
    public switchToManualMode(): void {
        this.currentDrawingMode = DrawingMode.MANUAL;
        console.log(' 切换到手动绘制模式');
        this.showModeStatus();
    }

    /**
     * 切换到自动识别模式
     */
    public switchToAutoDetectMode(): void {
        this.currentDrawingMode = DrawingMode.AUTO_DETECT;
        console.log('切换到自动识别模式');
        this.showModeStatus();
    }

    /**
     * 切换绘制模式
     */
    public toggleDrawingMode(): void {
        // 防抖处理
        if (this.modeToggleDebounce) {
            console.log('模式切换防抖中，忽略重复调用');
            return;
        }

        this.modeToggleDebounce = true;
        
        // 500ms后重置防抖状态
        setTimeout(() => {
            this.modeToggleDebounce = false;
        }, 500);

        const previousMode = this.currentDrawingMode;
        
        if (this.currentDrawingMode === DrawingMode.MANUAL) {
            this.currentDrawingMode = DrawingMode.AUTO_DETECT;
            console.log('切换：手动绘制模式 → 自动识别模式');
        } else {
            this.currentDrawingMode = DrawingMode.MANUAL;
            console.log('切换：自动识别模式 → 手动绘制模式');
        }
        
        // 确保模式真的改变了
        if (previousMode !== this.currentDrawingMode) {
            this.showModeStatus();
        } else {
            console.warn('模式切换失败，保持原状态');
        }
    }

    /**
     * 获取当前绘制模式
     */
    public getCurrentDrawingMode(): DrawingMode {
        return this.currentDrawingMode;
    }

    /**
     * 显示当前模式状态
     */
    private showModeStatus(): void {
        const modeText = this.currentDrawingMode === DrawingMode.MANUAL ? 
            '🖊️ 手动绘制模式' : '🎯 自动识别模式';
        
        console.log(`当前模式: ${modeText}`);
        
        // 可选：在界面上显示模式提示
        this.showModeToast(modeText);
    }

    /**
     * 显示模式切换提示
     */
    private showModeToast(message: string): void {
        // 移除现有提示
        const existingToast = document.getElementById('drawing-mode-toast');
        if (existingToast) {
            existingToast.remove();
        }

        const toast = document.createElement('div');
        toast.id = 'drawing-mode-toast';
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-family: Arial, sans-serif;
            z-index: 10000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        toast.textContent = message;

        document.body.appendChild(toast);

        // 淡入效果
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 10);

        // 3秒后自动消失
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    /**
     * 显示DXF封闭图形统计信息
     */
    public showDxfShapeStats(): void {
        if (!this.viewer || typeof this.viewer.getClosedShapeStats !== 'function') {
            console.warn('无法获取DXF封闭图形统计信息');
            return;
        }

        const stats = this.viewer.getClosedShapeStats();
        
        // 如果有图形被过滤，显示详细信息
        if (stats.largeShapesFiltered > 0) {
            console.log(` 已过滤掉 ${stats.largeShapesFiltered} 个面积过大的图形（超过总面积50%）`);
        }
    }

    /**
     * 计算指定线段的方向角度
     * @param shape 封闭图形
     * @returns 方向角度（0-360度）
     */
    private calculateSegmentDirection(shape: ClosedShape): number {
        if (!shape.segments || shape.segments.length === 0) return 0;
        
        const segmentIndex = shape.currentDirectionSegmentIndex || 0;
        const segment = shape.segments[segmentIndex];
        
        let startPoint = segment.start;
        let endPoint = segment.end;
        
        // 如果方向翻转，交换起点和终点
        if (shape.directionReversed) {
            [startPoint, endPoint] = [endPoint, startPoint];
        }
        
        const dx = endPoint.x - startPoint.x;
        const dy = endPoint.y - startPoint.y;
        
        // 计算角度（弧度转角度），水平向右为0度
        let angle = Math.atan2(dy, dx) * (180 / Math.PI);
        
        // 确保角度为正值（0-360度）
        if (angle < 0) {
            angle += 360;
        }
        
        // 保存到userData
        if (!shape.userData) {
            shape.userData = {};
        }
        shape.userData.direction = angle;
        
        return angle;
    }

    /**
     * 高亮显示方向线段
     * @param shape 封闭图形
     */
    private highlightDirectionSegment(shape: ClosedShape): void {
        if (!shape.segments || shape.segments.length === 0) return;
        
        // 重置所有线段颜色和渲染优先级
        shape.segments.forEach((segment, index) => {
            if (segment.line instanceof THREE.Line) {
                const material = segment.line.material as THREE.LineBasicMaterial;
                if (index === shape.currentDirectionSegmentIndex) {
                    // 当前方向线段设为红色高亮
                    material.color.setHex(0xff0000);
                    // 设置更高的渲染优先级，确保红色线条在最上层
                    segment.line.position.z = 1;
                    segment.line.renderOrder = 1040;
                    // 由于linewidth在WebGL中支持有限，我们通过创建更粗的线条几何体来实现
                    this.createThickDirectionLine(segment, shape);
                } else {
                    // 其他线段设为默认颜色
                    material.color.setHex(shape.isSelected ? 0xffaa00 : 0x0000ff);
                    segment.line.position.z = 0.1;
                    // 移除可能存在的粗线条
                    this.removeThickDirectionLine(segment);
                }
            }
        });
        
        // 重新渲染
        if (this.viewer) {
            this.viewer.render();
        }
    }

    /**
     * 创建粗的方向线条
     * @param segment 线段
     * @param shape 图形
     */
    private createThickDirectionLine(segment: any, shape: ClosedShape): void {
        // 如果已经有粗线条，先移除
        this.removeThickDirectionLine(segment);
        
        // 创建粗线条几何体 - 使用矩形模拟粗线
        const start = segment.start;
        const end = segment.end;
        const lineWidth = 60; // 线条宽度
        
        // 计算线条方向和垂直方向
        const direction = new THREE.Vector3().subVectors(end, start).normalize();
        const perpendicular = new THREE.Vector3(-direction.y, direction.x, 0).multiplyScalar(lineWidth / 2);
        
        // 创建矩形顶点
        const vertices = [
            start.x + perpendicular.x, start.y + perpendicular.y, start.z + 0.05,
            start.x - perpendicular.x, start.y - perpendicular.y, start.z + 0.05,
            end.x - perpendicular.x, end.y - perpendicular.y, end.z + 0.05,
            end.x + perpendicular.x, end.y + perpendicular.y, end.z + 0.05
        ];
        
        const indices = [0, 1, 2, 0, 2, 3];
        
        const geometry = new THREE.BufferGeometry();
        geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
        geometry.setIndex(indices);
        
        const material = new THREE.MeshBasicMaterial({
            color: 0xff0000,
            transparent: true,
            opacity: 0.8,
            side: THREE.DoubleSide
        });
        
        const thickLine = new THREE.Mesh(geometry, material);
        thickLine.renderOrder = 1040;
        thickLine.position.z = 1;
        
        this.scene?.add(thickLine);
        segment.thickLine = thickLine; // 保存引用以便后续清理
    }

    /**
     * 移除粗的方向线条
     * @param segment 线段
     */
    private removeThickDirectionLine(segment: any): void {
        if (segment.thickLine) {
            this.scene?.remove(segment.thickLine);
            segment.thickLine = undefined;
        }
    }

    /**
     * 更新方向指示器（小圆点）
     * @param shape 封闭图形
     */
    private updateDirectionIndicator(shape: ClosedShape): void {
        // 移除现有的方向指示器
        if (shape.directionIndicator) {
            this.scene?.remove(shape.directionIndicator);
            shape.directionIndicator = undefined;
        }
        
        if (!shape.segments || shape.segments.length === 0) return;
        
        const segmentIndex = shape.currentDirectionSegmentIndex || 0;
        const segment = shape.segments[segmentIndex];
        
        let startPoint = segment.start;
        let endPoint = segment.end;
        
        // 如果方向翻转，交换起点和终点
        if (shape.directionReversed) {
            [startPoint, endPoint] = [endPoint, startPoint];
        }
        
        // 计算方向向量
        const dx = endPoint.x - startPoint.x;
        const dy = endPoint.y - startPoint.y;
        const length = Math.sqrt(dx * dx + dy * dy);
        
        if (length === 0) return;
        
        // 标准化方向向量
        const normalizedDx = dx / length;
        const normalizedDy = dy / length;
        
        // 在起点左侧放置小圆点（垂直于线段方向）
        const perpX = -normalizedDy; // 垂直向量
        const perpY = normalizedDx;
        
        const indicatorOffset = 0; // 圆点距离线段的距离
        const indicatorPosition = new THREE.Vector3(
            startPoint.x + perpX * indicatorOffset,
            startPoint.y + perpY * indicatorOffset,
            0.3
        );
        
        // 创建小圆点
        const geometry = new THREE.CircleGeometry(60, 16);
        const material = new THREE.MeshBasicMaterial({
            color: 0x00ff00,
            transparent: true,
            opacity: 0.9
        });
        
        const indicator = new THREE.Mesh(geometry, material);
        indicator.position.copy(indicatorPosition);
        indicator.renderOrder = 1041; // 最高渲染优先级
        indicator.position.z = 1.1;
        
        this.scene?.add(indicator);
        shape.directionIndicator = indicator;
        
        // 重新渲染
        if (this.viewer) {
            this.viewer.render();
        }
    }

    /**
     * 清除图形的方向指示器
     * @param shape 封闭图形
     */
    private clearDirectionIndicator(shape: ClosedShape): void {
        if (shape.directionIndicator) {
            this.scene?.remove(shape.directionIndicator);
            shape.directionIndicator = undefined;
        }
        
        // 清除所有粗线条
        if (shape.segments) {
            shape.segments.forEach(segment => {
                this.removeThickDirectionLine(segment);
            });
        }
    }

}