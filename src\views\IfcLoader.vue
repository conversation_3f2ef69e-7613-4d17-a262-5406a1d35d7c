<template>
  <div class="ifc-loader-page">
    <!-- 主容器 -->
    <div class="main-container">
      <!-- 3D 查看器容器 -->
      <div id="ifc-container" ref="ifcContainer" class="viewer-container"></div>

      <!-- 左侧控制面板 -->
      <div class="left-panel">
        <div class="panel-section">
          <h3>IFC 文件操作</h3>
          <input ref="fileInput" type="file" style="display: none;" @change="loadIFC" accept=".ifc" />
          <button @click="fileInput?.click()" class="btn btn-primary">
            加载 IFC 文件
          </button>
          <button @click="showWalls" class="btn btn-primary">
            显示墙体
          </button>
          <button @click="showCeilings" class="btn btn-primary">
            显示天花/地板
          </button>
          <button @click="switchViewPort" class="btn btn-primary">
            切换视角
          </button>
          <button @click="restoreCamera" class="btn btn-primary"> 恢复视角 </button>

          <div v-if="currentFileName" class="file-info">
            <p>当前文件: {{ currentFileName }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onUnmounted, onMounted } from 'vue'
import { IfcManager } from '../../packages/ifc-loader/src/index'

const showWall = ref(false)
const showCeiling = ref(false)
const ifcContainer = ref<HTMLElement>()
const fileInput = ref<HTMLInputElement>()
const currentFileName = ref('')

// 存储IfcManager实例的引用
let ifcManagerInstance: any = null

const showWalls = () => {
  try {
    ifcManagerInstance.toggleWalls(showWall.value)
    showWall.value = !showWall.value
  } catch (error) {
    console.error('显示墙体时出错:', error)
  }
}

const switchViewPort = () => {
  ifcManagerInstance.switchToTopViewMode()
}

const restoreCamera = () => {
  ifcManagerInstance.restoreCamera()
}

const showCeilings = () => {
  try {
    ifcManagerInstance.toggleSlabs(showCeiling.value)
    showCeiling.value = !showCeiling.value
  } catch (error) {
    console.error('显示天花板时出错:', error)
  }
}

const loadIFC = async (event: Event) => {
  try {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (!file) {
      return
    }
    currentFileName.value = file.name.split('.').slice(0, -1).join('.')
    ifcManagerInstance = IfcManager.getInstance(ifcContainer.value as HTMLElement)
    await ifcManagerInstance.init()
    await ifcManagerInstance.loadFromFile(file)
  } catch (error: any) {
    console.error('加载IFC文件时出错:', error)
    currentFileName.value = ''
  }
}

// 组件挂载时的初始化
onMounted(() => {
  try {
    console.log('IFC加载器组件已挂载')
  } catch (error: any) {
    console.error('组件挂载时出错:', error)
  }
})

// 组件卸载时的清理工作
onUnmounted(() => {
  try {
    console.log('正在清理IFC加载器组件...')

    // 清理IfcManager实例
    if (ifcManagerInstance) {
      if (typeof ifcManagerInstance.dispose === 'function') {
        ifcManagerInstance.dispose()
      }
      ifcManagerInstance = null
    }

    // 清理单例实例
    if (IfcManager.destroy) {
      IfcManager.destroy()
    }

    // 重置状态
    showWall.value = false
    showCeiling.value = false
    currentFileName.value = ''

    console.log('IFC加载器组件清理完成')
  } catch (error: any) {
    console.error('组件卸载时清理资源出错:', error)
  }
})

</script>

<style scoped>
.ifc-loader-page {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #f5f5f5;
}

.main-container {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
}

.viewer-container {
  flex: 1;
  height: 100%;
  position: relative;
}

.left-panel {
  width: 320px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-left: 1px solid #e0e0e0;
  padding: 20px;
  overflow-y: auto;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

.panel-section {
  margin-bottom: 25px;
  padding: 15px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.panel-section h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

.btn {
  width: 100%;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-warning {
  background: linear-gradient(45deg, #f093fb, #f5576c);
  color: white;
}

.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(240, 147, 251, 0.4);
}

.btn-danger {
  background: linear-gradient(45deg, #ff9a9e, #fecfef);
  color: #333;
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 154, 158, 0.4);
}

.file-info {
  margin-top: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #667eea;
}

.file-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
  word-break: break-all;
}

.loading-info {
  text-align: center;
}

.loading-info p {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
}

.loading-bar {
  width: 100%;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.loading-progress {
  height: 100%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  animation: loading 2s infinite;
}

@keyframes loading {
  0% {
    width: 0%;
  }

  50% {
    width: 100%;
  }

  100% {
    width: 0%;
  }
}

.element-info {
  font-size: 14px;
  line-height: 1.6;
}

.element-info p {
  margin: 0 0 8px 0;
  color: #666;
}

.element-info strong {
  color: #333;
}

.element-info h4 {
  margin: 15px 0 10px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.properties-list {
  max-height: 200px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 10px;
}

.property-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid #e9ecef;
}

.property-item:last-child {
  border-bottom: none;
}

.property-name {
  font-weight: 500;
  color: #495057;
  flex-shrink: 0;
  margin-right: 10px;
}

.property-value {
  color: #6c757d;
  text-align: right;
  word-break: break-all;
  font-size: 13px;
}
</style>