# 线条方向索引和翻转状态修复

## 问题描述
在导入训练数据时，缺少了线条索引和方向翻转状态的信息，导致导入的图形无法正确恢复用户设置的方向线条和翻转状态。

## 解决方案

### 1. 导出时保存方向信息
修改了 `categorizeShapes()` 方法，在导出训练数据时保存以下信息：
- `directionSegmentIndex`: 用户选择的方向线条索引
- `directionReversed`: 方向是否翻转
- 使用 `calculateSegmentDirection()` 而不是 `calculateShapeAngle()` 来计算方向

### 2. 导入时恢复方向信息
修改了以下方法的签名和实现：
- `createShapeFromData()`: 添加了 `directionSegmentIndex` 和 `directionReversed` 参数
- `createShapeFromPoints()`: 添加了 `directionSegmentIndex` 和 `directionReversed` 参数

### 3. 修改的文件
- `packages/dxf-renderer/src/drawing/free_draw_manager.ts`

### 4. 主要修改点

#### 4.1 导出数据结构修改
```typescript
// CAD对象
const cadObject: any = {
    itemName: description,
    data: dataArray,
    id: this.onjectMap.get(description),
    libraryName:"华为",
    // 新增：保存方向相关信息
    directionSegmentIndex: shape.currentDirectionSegmentIndex || 0,
    directionReversed: shape.directionReversed || false
};

// 分区对象
const partitionObject: any = {
    itemName: description,
    data: coordinates,
    // 新增：保存方向相关信息
    directionSegmentIndex: shape.currentDirectionSegmentIndex || 0,
    directionReversed: shape.directionReversed || false
};

// 布局对象
layoutObjects.push({
    itemName: description || '未知对象',
    data: dataArray,
    parent: this.findParentRoom(shape),
    libraryName:"华为",
    // 新增：保存方向相关信息
    directionSegmentIndex: shape.currentDirectionSegmentIndex || 0,
    directionReversed: shape.directionReversed || false
});
```

#### 4.2 导入逻辑修改
```typescript
// 解析方向信息
const directionSegmentIndex = typeof obj.directionSegmentIndex === 'number' ? obj.directionSegmentIndex : 0;
const directionReversed = typeof obj.directionReversed === 'boolean' ? obj.directionReversed : false;

// 传递给创建方法
this.createShapeFromData(obj.itemName, obj.data, hasDirection, area, crowdDensity, objectId, directionSegmentIndex, directionReversed);
```

#### 4.3 图形创建时恢复状态
```typescript
const closedShape: ClosedShape = {
    id: shapeId,
    objectId: objectId,
    segments: segments,
    description: description,
    group: group,
    isSelected: false,
    // 使用传入的参数恢复状态
    currentDirectionSegmentIndex: directionSegmentIndex !== undefined ? directionSegmentIndex : 0,
    directionReversed: directionReversed !== undefined ? directionReversed : false,
    userData: {
        direction: direction,
        area: area,
        crowdDensity: crowdDensity
    }
};
```

### 5. 功能验证
- 导出时会保存用户选择的线条索引和翻转状态
- 导入时会正确恢复这些状态
- 方向计算使用 `calculateSegmentDirection()` 方法，考虑了用户的选择
- 控制台会输出详细的创建信息，包括方向、线条索引和翻转状态

### 6. 使用方法
1. 在界面上绘制图形
2. 点击图形，在弹出的对话框中选择方向线条和设置翻转状态
3. 导出训练数据（会保存所有方向信息）
4. 导入训练数据时，图形会自动恢复到之前设置的方向状态

### 7. 注意事项
- 确保导出的JSON文件包含 `directionSegmentIndex` 和 `directionReversed` 字段
- 旧版本的JSON文件导入时会使用默认值（索引0，不翻转）
- 方向计算基于用户选择的线条，而不是固定的第一条线条
