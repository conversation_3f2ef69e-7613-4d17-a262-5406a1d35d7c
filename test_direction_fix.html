<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方向索引修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .code-block {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
        }
        .warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>线条方向索引和翻转状态修复测试</h1>
        
        <div class="test-section">
            <h3>✅ 修复完成</h3>
            <p class="success">已成功修复导入训练数据时缺少线条索引和方向翻转状态的问题。</p>
        </div>

        <div class="test-section">
            <h3>🔧 主要修改</h3>
            <ul>
                <li><strong>导出时保存方向信息：</strong>在 categorizeShapes() 方法中添加 directionSegmentIndex 和 directionReversed 字段</li>
                <li><strong>导入时恢复方向信息：</strong>修改 createShapeFromData() 和 createShapeFromPoints() 方法签名</li>
                <li><strong>使用正确的方向计算：</strong>使用 calculateSegmentDirection() 而不是 calculateShapeAngle()</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>📋 测试步骤</h3>
            <ol>
                <li>打开 DXF 标注工具</li>
                <li>绘制一个图形（如门、墙等）</li>
                <li>点击图形，在弹出的对话框中：
                    <ul>
                        <li>使用 📏 按钮切换方向线条</li>
                        <li>使用 🔄 按钮翻转方向</li>
                        <li>观察方向角度的变化</li>
                    </ul>
                </li>
                <li>导出训练数据（按 X 键或点击导出按钮）</li>
                <li>清空所有图形</li>
                <li>导入刚才导出的数据（按 L 键或点击加载按钮）</li>
                <li>验证图形的方向线条索引和翻转状态是否正确恢复</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>📊 导出数据格式示例</h3>
            <div class="code-block">
{
  "CAD": [
    {
      "itemName": "底图实物",
      "data": [
        {
          "itemName": "门",
          "data": [45.0, 100, 200, 300, 200, 300, 400, 100, 400],
          "id": 1,
          "libraryName": "华为",
          "directionSegmentIndex": 2,  // 新增：方向线条索引
          "directionReversed": true,   // 新增：方向是否翻转
          "crowdDensity": 50
        }
      ]
    }
  ]
}
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 预期结果</h3>
            <ul>
                <li class="success">导出的 JSON 文件包含 directionSegmentIndex 和 directionReversed 字段</li>
                <li class="success">导入时图形能正确恢复到之前设置的方向状态</li>
                <li class="success">控制台输出包含详细的方向信息</li>
                <li class="info">旧版本的 JSON 文件仍能正常导入（使用默认值）</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>⚠️ 注意事项</h3>
            <ul>
                <li class="warning">确保在测试前已重新编译项目</li>
                <li class="warning">测试时注意观察控制台输出的日志信息</li>
                <li class="warning">如果遇到问题，检查浏览器开发者工具的错误信息</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🚀 快捷键提醒</h3>
            <ul>
                <li><strong>A</strong> - 开始绘制模式</li>
                <li><strong>X</strong> - 导出训练数据</li>
                <li><strong>L</strong> - 导入训练数据</li>
                <li><strong>ESC</strong> - 结束绘制模式</li>
                <li><strong>Ctrl+Z</strong> - 撤销操作</li>
            </ul>
        </div>
    </div>
</body>
</html>
