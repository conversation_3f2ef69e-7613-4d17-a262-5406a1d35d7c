import * as THREE from 'three';
import { <PERSON>ufferGeometry, Color, Float32BufferAttribute, Vector3 } from 'three';
import { OrbitControls } from './OrbitControls';
import bSpline from './bspline';
import { Text } from 'troika-three-text';
import { parseDxfMTextContent } from '@dxfom/mtext';
import Stats from 'stats.js';
const textControlCharactersRegex = /\\[AXQWOoLIpfH].*;/g;
const curlyBraces = /\\[{}]/g;

// Three.js extension functions. Webpack doesn't seem to like it if we modify the THREE object directly.
var THREEx = { Math: {} };
/**
 * Returns the angle in radians of the vector (p1,p2). In other words, imagine
 * putting the base of the vector at coordinates (0,0) and finding the angle
 * from vector (1,0) to (p1,p2).
 * @param  {Object} p1 start point of the vector
 * @param  {Object} p2 end point of the vector
 * @return {Number} the angle
 */
THREEx.Math.angle2 = function (p1, p2) {
    var v1 = new THREE.Vector2(p1.x, p1.y);
    var v2 = new THREE.Vector2(p2.x, p2.y);
    v2.sub(v1); // sets v2 to be our chord
    v2.normalize();
    if (v2.y < 0) return -Math.acos(v2.x);
    return Math.acos(v2.x);
};


THREEx.Math.polar = function (point, distance, angle) {
    var result = {};
    result.x = point.x + distance * Math.cos(angle);
    result.y = point.y + distance * Math.sin(angle);
    return result;
};

/**
 * Calculates points for a curve between two points using a bulge value. Typically used in polylines.
 * @param startPoint - the starting point of the curve
 * @param endPoint - the ending point of the curve
 * @param bulge - a value indicating how much to curve
 * @param segments - number of segments between the two given points
 */
function getBulgeCurvePoints(startPoint, endPoint, bulge, segments) {

    var vertex, i,
        center, p0, p1, angle,
        radius, startAngle,
        thetaAngle;

    var obj = {};
    obj.startPoint = p0 = startPoint ? new THREE.Vector2(startPoint.x, startPoint.y) : new THREE.Vector2(0, 0);
    obj.endPoint = p1 = endPoint ? new THREE.Vector2(endPoint.x, endPoint.y) : new THREE.Vector2(1, 0);
    obj.bulge = bulge = bulge || 1;

    angle = 4 * Math.atan(bulge);
    radius = p0.distanceTo(p1) / 2 / Math.sin(angle / 2);
    center = THREEx.Math.polar(startPoint, radius, THREEx.Math.angle2(p0, p1) + (Math.PI / 2 - angle / 2));

    obj.segments = segments = segments || Math.max(Math.abs(Math.ceil(angle / (Math.PI / 18))), 6); // By default want a segment roughly every 10 degrees
    startAngle = THREEx.Math.angle2(center, p0);
    thetaAngle = angle / segments;

    var vertices = [];

    vertices.push(new THREE.Vector3(p0.x, p0.y, 0));

    for (i = 1; i <= segments - 1; i++) {
        vertex = THREEx.Math.polar(center, Math.abs(radius), startAngle + thetaAngle * i);
        vertices.push(new THREE.Vector3(vertex.x, vertex.y, 0));
    }

    return vertices;
};

/**
 * Viewer class for a dxf object.
 * @param {Object} data - the dxf object
 * @param {Object} parent - the parent element to which we attach the rendering canvas
 * @param {Number} width - width of the rendering canvas in pixels
 * @param {Number} height - height of the rendering canvas in pixels
 * @param {String|null} font - a font URL for troika-three-text or null for default font
 * @constructor
 */
export function Viewer(data, parent, width, height, font) {
    var self = this;

    createLineTypeShaders(data);

    var scene = new THREE.Scene();

    // 字体URL - 使用本地的 OTF 字体文件
    var fontUrl = font || '/111font.otf';

    // 图层管理相关
    var layerMap = new Map(); // 存储图层信息的Map结构
    var layerGroups = new Map(); // 存储每个图层的Group对象
    // 性能优化：材质和几何体缓存
    var materialCache = new Map();
    var geometryCache = new Map();
    /**
     * 获取或创建缓存的材质
     */
    function getCachedMaterial(color, type = 'line') {
        var key = `${type}_${color}`;
        if (!materialCache.has(key)) {
            var material;
            if (type === 'line') {
                material = new THREE.LineBasicMaterial({
                    color: color
                    // 移除linewidth属性，避免WebGL错误
                });
            } else if (type === 'mesh') {
                material = new THREE.MeshBasicMaterial({
                    color: color,
                    side: THREE.DoubleSide
                });
            } else if (type === 'point') {
                material = new THREE.PointsMaterial({
                    color: color,
                    size: 2
                });
            }
            materialCache.set(key, material);
        }
        return materialCache.get(key);
    }

    /**
     * 获取或创建缓存的几何体
     */
    function getCachedGeometry(type, params) {
        var key = `${type}_${JSON.stringify(params)}`;
        if (!geometryCache.has(key)) {
            var geometry;
            switch (type) {
                case 'circle':
                    geometry = new THREE.CircleGeometry(params.radius, params.segments || 32);
                    break;
                case 'ring':
                    geometry = new THREE.RingGeometry(params.innerRadius, params.outerRadius, params.segments || 32);
                    break;
                case 'box':
                    geometry = new THREE.BoxGeometry(params.width || 1, params.height || 1, params.depth || 1);
                    break;
                case 'plane':
                    geometry = new THREE.PlaneGeometry(params.width || 1, params.height || 1);
                    break;
                default:
                    geometry = new THREE.BufferGeometry();
            }
            geometryCache.set(key, geometry);
        }
        return geometryCache.get(key).clone();
    }



    /**
     * 处理文字换行 - 如果单行超过70个字符就换行，每20个字符换一行
     * @param {string} text - 原始文本
     * @returns {string} 处理后的文本
     */
    function processTextWrapping(text) {
        if (!text) return '';

        var lines = text.split('\n');
        var processedLines = [];

        for (var i = 0; i < lines.length; i++) {
            var line = lines[i];

            // 如果单行超过70个字符，进行换行处理
            if (line.length > 70) {
                // 每20个字符换一行
                var wrappedLines = [];
                for (var j = 0; j < line.length; j += 23) {
                    wrappedLines.push(line.substring(j, j + 23));
                }
                processedLines.push(wrappedLines.join('\n'));
            } else {
                processedLines.push(line);
            }
        }

        return processedLines.join('\n');
    }

    /**
     * 使用 Troika-3d-text 创建文字
     */
    function createTroikaText(textContent, style, entity, color) {
        if (!textContent) return null;
    
        // 创建 Troika Text 对象
        var text = new Text();
    
        // 设置文字内容，处理换行符和自动换行
        var processedText = textContent
            .replace(/\\P/g, '\n')           // DXF换行符
            .replace(/\\p/g, '\n')           // 小写p也是换行符
            .replace(/\r\n/g, '\n')          // Windows换行符
            .replace(/\r/g, '\n')            // Mac换行符
            .replace(/\\~/g, ' ')            // 不间断空格
            .replace(/\\\\/g, '\\')          // 转义的反斜杠
            .replace(/\\{/g, '{')            // 转义的左大括号
            .replace(/\\}/g, '}');           // 转义的右大括号
    
        // 应用自动换行处理
        text.text = processTextWrapping(processedText);
    
        // 设置字体大小 - 移除不必要的放大
        var originalFontSize = style.textHeight || 10;
        text.fontSize = originalFontSize;
    
        // 设置颜色
        if (color && typeof color.getHexString === 'function') {
            text.color = color.getHex();
        } else if (typeof color === 'string') {
            text.color = color.startsWith('#') ? color : '#' + color;
        } else if (typeof color === 'number') {
            text.color = color;
        } else {
            text.color = 0x00ffff; // 默认青色
        }
    
        // 设置字体
        text.font = fontUrl;
    
        // 设置对齐方式
        var horizontalAlign = style.horizontalAlignment || 'left';
        var verticalAlign = style.verticalAlignment || 'baseline';
    
        text.anchorX = horizontalAlign === 'center' ? 'center' :
                      horizontalAlign === 'right' ? 'right' : 'left';
    
        // 垂直对齐处理 - 针对DXF标准进行调整
        if (verticalAlign === 'baseline') {
            // 对于baseline，使用bottom锚点更接近DXF的基线定义
            text.anchorY = 'bottom';
            if(text.text.length > 70){
                text.anchorY = 'top';
                text.anchorX = 'top';
            }
        } else if (verticalAlign === 'middle') {
            text.anchorY = 'middle';
        } else if (verticalAlign === 'top') {
            text.anchorY = 'top';
        } else {
            text.anchorY = 'bottom'; // 默认使用bottom
        }
    
        // 设置位置
        var posX = entity.position.x;
        var posY = entity.position.y;
        var posZ = 0;
    
        // 根据垂直对齐方式进行精确的位置调整
        if (text.anchorY === 'bottom') {
            // 对于bottom锚点，需要向上调整以匹配DXF的baseline
            // 调整量约为字体大小的10-20%（根据具体字体调整）
            posY += originalFontSize * 0.15;
        } else if (text.anchorY === 'middle') {
            // 对于middle对齐，可能需要微调
            posY += originalFontSize * 0.05;
        }
    
        text.position.set(posX, posY, posZ);
    
        // 设置旋转
        if (entity.rotation) {
            text.rotation.z = entity.rotation * Math.PI / 180;
        }
    
        // 同步渲染（必须调用）
        text.sync();
    
        return text;
    }





    // 初始化Stats.js性能监控
    // var stats = new Stats();
    // stats.showPanel(0); // 0: fps, 1: ms, 2: mb, 3+: custom
    // stats.dom.style.position = 'absolute';
    // stats.dom.style.top = '10px';
    // stats.dom.style.right = '10px';
    // stats.dom.style.zIndex = '1000';
    // parent.appendChild(stats.dom);

    // 视锥剔除相关
    var frustum = new THREE.Frustum();
    var cameraMatrix = new THREE.Matrix4();
    var cullableObjects = []; // 存储可剔除的对象

    // Create scene from dxf object (data)
    var i, entity, obj, min_x, min_y, min_z, max_x, max_y, max_z;
    var dims = {
        min: { x: 0, y: 0, z: 0 },
        max: { x: 0, y: 0, z: 0 }
    };
   var dxfDataPoint = new Map()
    // 存储封闭图形的数组
    var closedShapes = [];
    
    // 预处理图层信息
    initializeLayers(data);
    
    for (i = 0; i < data.entities.length; i++) {
        entity = data.entities[i];
        obj = drawEntity(entity, data, null);

        if (obj) {
            var bbox = new THREE.Box3().setFromObject(obj);
            if (isFinite(bbox.min.x) && (dims.min.x > bbox.min.x)) dims.min.x = bbox.min.x;
            if (isFinite(bbox.min.y) && (dims.min.y > bbox.min.y)) dims.min.y = bbox.min.y;
            if (isFinite(bbox.min.z) && (dims.min.z > bbox.min.z)) dims.min.z = bbox.min.z;
            if (isFinite(bbox.max.x) && (dims.max.x < bbox.max.x)) dims.max.x = bbox.max.x;
            if (isFinite(bbox.max.y) && (dims.max.y < bbox.max.y)) dims.max.y = bbox.max.y;
            if (isFinite(bbox.max.z) && (dims.max.z < bbox.max.z)) dims.max.z = bbox.max.z;
            
            // 将实体添加到对应的图层组中
            var layerName = entity.layer || '0';
            addEntityToLayer(obj, layerName);
        }
        obj = null;
    }

    width = width || parent.clientWidth;
    height = height || parent.clientHeight;
    var aspectRatio = width / height;

    var upperRightCorner = { x: dims.max.x, y: dims.max.y };
    var lowerLeftCorner = { x: dims.min.x, y: dims.min.y };

    // Figure out the current viewport extents
    var vp_width = upperRightCorner.x - lowerLeftCorner.x;
    var vp_height = upperRightCorner.y - lowerLeftCorner.y;
    var center = center || {
        x: vp_width / 2 + lowerLeftCorner.x,
        y: vp_height / 2 + lowerLeftCorner.y
    };

    // Fit all objects into current ThreeDXF viewer
    var extentsAspectRatio = Math.abs(vp_width / vp_height);
    if (aspectRatio > extentsAspectRatio) {
        vp_width = vp_height * aspectRatio;
    } else {
        vp_height = vp_width / aspectRatio;
    }

    var viewPort = {
        bottom: -vp_height / 2,
        left: -vp_width / 2,
        top: vp_height / 2,
        right: vp_width / 2,
        center: {
            x: center.x,
            y: center.y
        }
    };

    var camera = new THREE.OrthographicCamera(viewPort.left, viewPort.right, viewPort.top, viewPort.bottom, 1, 19);
    camera.position.z = 10;
    camera.position.x = viewPort.center.x;
    camera.position.y = viewPort.center.y;

    var renderer = this.renderer = new THREE.WebGLRenderer();
    renderer.setSize(width, height);
    renderer.setClearColor(0x212830, 1);

    parent.appendChild(renderer.domElement);
    parent.style.display = 'block';

    //TODO: Need to make this an option somehow so others can roll their own controls.
    var controls = new OrbitControls(camera, parent);
    controls.target.x = camera.position.x;
    controls.target.y = camera.position.y;
    controls.target.z = 0;
    controls.zoomSpeed = 3;

    //Uncomment this to disable rotation (does not make much sense with 2D drawings).
    //controls.enableRotate = false;

    // 视锥剔除函数
    function updateFrustumCulling() {
        // 更新相机矩阵
        camera.updateMatrixWorld();
        cameraMatrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);
        frustum.setFromProjectionMatrix(cameraMatrix);

        // 对可剔除对象进行视锥剔除
        cullableObjects.forEach(function(obj) {
            if (obj && obj.geometry) {
                // 计算对象的包围盒
                if (!obj.geometry.boundingBox) {
                    obj.geometry.computeBoundingBox();
                }

                if (obj.geometry.boundingBox) {
                    // 创建世界空间的包围盒
                    var box = obj.geometry.boundingBox.clone();
                    box.applyMatrix4(obj.matrixWorld);

                    // 检查是否在视锥内
                    obj.visible = frustum.intersectsBox(box);
                }
            }
        });
    }

    this.render = function () {
        // stats.begin();

        // 更新视锥剔除（降低频率以提高性能）
        // if (Math.random() < 0.1) { // 10%的概率更新
            // updateFrustumCulling();
        // }
        renderer.render(scene, camera);
        // stats.end();
    };

    controls.addEventListener('change', this.render);
    this.render();
    controls.update();

    this.resize = function (width, height) {
        var originalWidth = renderer.domElement.width;
        var originalHeight = renderer.domElement.height;

        var hscale = width / originalWidth;
        var vscale = height / originalHeight;


        camera.top = (vscale * camera.top);
        camera.bottom = (vscale * camera.bottom);
        camera.left = (hscale * camera.left);
        camera.right = (hscale * camera.right);

        camera.updateProjectionMatrix();
        controls.update();
        renderer.setSize(width, height);
        renderer.setClearColor(0x212830, 1);
        this.render();
    };

    this.getDxfDataPoint = function () {
        return dxfDataPoint;
    }

    this.getScene = function () {
        return scene;
    }

    this.getCamera = function () {
        return camera;
    }

    // 注册可剔除对象
    this.registerCullableObject = function(obj) {
        if (obj && cullableObjects.indexOf(obj) === -1) {
            cullableObjects.push(obj);
        }
    }

    // 手动更新视锥剔除
    this.updateCulling = function() {
        updateFrustumCulling();
    }

    // 获取性能统计
    // this.getStats = function() {
    //     return {
    //         fps: Math.round(1000 / stats.dom.children[0].textContent.split(' ')[0]),
    //         totalObjects: scene.children.length,
    //         cullableObjects: cullableObjects.length,
    //         visibleObjects: cullableObjects.filter(function(obj) { return obj.visible; }).length
    //     };
    // }

    function drawEntity(entity, data, parentGroup) {
        // 只处理有顶点的实体，并且去重存储
        if (entity.vertices && Array.isArray(entity.vertices)) {
            for (var i = 0; i < entity.vertices.length; i++) {
                var vertex = entity.vertices[i];

                // 跳过bulge属性，只存储顶点坐标
                if (vertex && typeof vertex.x === 'number' && typeof vertex.y === 'number') {
                    var transformedVertex = { x: vertex.x, y: vertex.y, z: vertex.z || 0 };

                    // 如果有父组（块），应用变换
                    if (parentGroup) {
                        // 创建临时向量进行变换
                        var tempVector = new THREE.Vector3(vertex.x, vertex.y, vertex.z || 0);

                        // 应用父组的变换矩阵
                        tempVector.applyMatrix4(parentGroup.matrixWorld);

                        transformedVertex.x = tempVector.x;
                        transformedVertex.y = tempVector.y;
                        transformedVertex.z = tempVector.z;
                    }

                    // 创建顶点的唯一键（基于变换后的坐标，精确到小数点后3位避免浮点误差）
                    var vertexKey = Math.round(transformedVertex.x * 1000) + ',' + Math.round(transformedVertex.y * 1000);

                    // 使用has()方法检查是否已存在，实现高性能去重
                    if (!dxfDataPoint.has(vertexKey)) {
                        // 存储变换后的顶点数据
                        dxfDataPoint.set(vertexKey, transformedVertex);
                    }
                }
            }
        }
        
        var mesh;
        if (entity.type === 'CIRCLE' || entity.type === 'ARC') {
            mesh = drawArc(entity, data);
            // 检查CIRCLE是否可以作为封闭图形
            if (entity.type === 'CIRCLE') {
                checkCircleAsClosedShape(entity, data, parentGroup);
            }
        } else if (entity.type === 'LWPOLYLINE' || entity.type === 'LINE' || entity.type === 'POLYLINE') {
            mesh = drawLine(entity, data);
            // 检查是否是封闭图形并存储
            checkAndStoreClosedShape(entity, data, parentGroup);
        } else if (entity.type === 'TEXT') {
            mesh = drawText(entity, data);
        } else if (entity.type === 'SOLID') {
            mesh = drawSolid(entity, data);
            // 检查SOLID是否可以作为封闭图形
            checkSolidAsClosedShape(entity, data, parentGroup);
        } else if (entity.type === 'HATCH') {
            mesh = drawHatch(entity, data);
            // 检查HATCH是否可以作为封闭图形
            checkHatchAsClosedShape(entity, data, parentGroup);
        } else if (entity.type === 'POINT') {
            mesh = drawPoint(entity, data);
        } else if (entity.type === 'INSERT') {
            mesh = drawBlock(entity, data);
        } else if (entity.type === 'SPLINE') {
            mesh = drawSpline(entity, data);
        } else if (entity.type === 'MTEXT') {
            mesh = drawMtext(entity, data);
        } else if (entity.type === 'ELLIPSE') {
            mesh = drawEllipse(entity, data);
        } else if (entity.type === 'DIMENSION') {
            var dimTypeEnum = entity.dimensionType & 7;
            if (dimTypeEnum === 0) {
                mesh = drawDimension(entity, data);
            } else {
                console.log("Unsupported Dimension type: " + dimTypeEnum);
            }
        } else if (entity.type === 'REGION') {
            // 处理REGION实体（通常是填充区域）
            mesh = drawRegion(entity, data);
            // 检查REGION是否可以作为封闭图形
            checkRegionAsClosedShape(entity, data, parentGroup);
        }
        else {
        }

        // 注册到可剔除对象列表（用于视锥剔除）
        if (mesh && mesh.geometry) {
            cullableObjects.push(mesh);
        }

        return mesh;
    }

    function drawEllipse(entity, data) {
        var color = getColor(entity, data);

        var xrad = Math.sqrt(Math.pow(entity.majorAxisEndPoint.x, 2) + Math.pow(entity.majorAxisEndPoint.y, 2));
        var yrad = xrad * entity.axisRatio;
        var rotation = Math.atan2(entity.majorAxisEndPoint.y, entity.majorAxisEndPoint.x);

        var curve = new THREE.EllipseCurve(
            entity.center.x, entity.center.y,
            xrad, yrad,
            entity.startAngle, entity.endAngle,
            false, // Always counterclockwise
            rotation
        );

        var points = curve.getPoints(50);
        var geometry = new THREE.BufferGeometry().setFromPoints(points);

        // 使用统一的材质创建函数
        var material = createLineMaterial(entity, data, color);

        // 如果是虚线材质，需要计算线段距离
        var ellipse = new THREE.Line(geometry, material);
        if (material instanceof THREE.LineDashedMaterial) {
            ellipse.computeLineDistances();
        }

        return ellipse;
    }

    function drawMtext(entity, data) {
        var color = getColor(entity, data);

        if (!entity.text) return null;

        // 提取纯文本
        var plainText;
        try {
            // 尝试使用外部解析库
            var textAndControlChars = parseDxfMTextContent(entity.text);
            plainText = extractPlainTextFromMtext(textAndControlChars);
        } catch (error) {
            console.warn('MTEXT解析失败，使用简化处理:', error);
            // 回退到简化处理
            plainText = processMtextContent(entity.text);
        }

        var style = {
            horizontalAlignment: getMtextAlignment(entity.attachmentPoint),
            textHeight: entity.height || 10
        };

        // 使用 Troika 创建文字
        return createTroikaText(plainText, style, entity, color);
    }

    /**
     * 从解析的MTEXT结构中提取纯文本
     */
    function extractPlainTextFromMtext(textAndControlChars) {
        var text = [];
        
        for (let item of textAndControlChars) {
            if (typeof item === 'string') {
                // 跳过格式化控制代码
                if (!item.startsWith('\\') || item === '\\P' || item === '\\X') {
                    if (item === '\\P') {
                        text.push('\n');
                    } else if (item !== '\\X') {
                        text.push(item);
                    }
                }
            } else if (Array.isArray(item)) {
                // 递归处理嵌套结构
                text.push(extractPlainTextFromMtext(item));
            } else if (typeof item === 'object') {
                if (item['S'] && item['S'].length === 3) {
                    // 处理分数格式
                    text.push(item['S'][0] + '/' + item['S'][2]);
                }
            }
        }
        
        return text.join('');
    }

    /**
     * 处理MTEXT内容 - 简化版本
     */
    function processMtextContent(text) {
        if (!text) return '';

        // 处理DXF特有的换行符和控制字符
        return text
            .replace(/\\P/g, '\n')           // DXF换行符
            .replace(/\\p/g, '\n')           // 小写p也是换行符
            .replace(/\r\n/g, '\n')          // Windows换行符
            .replace(/\r/g, '\n')            // Mac换行符
            .replace(/\\~/g, ' ')            // 不间断空格
            .replace(/\\\\/g, '\\')          // 转义的反斜杠
            .replace(/\\{/g, '{')            // 转义的左大括号
            .replace(/\\}/g, '}');           // 转义的右大括号
    }

    /**
     * 根据MTEXT附加点获取对齐方式
     */
    function getMtextAlignment(attachmentPoint) {
        switch (attachmentPoint) {
            case 1: case 4: case 7: return 'left';    // 左对齐
            case 2: case 5: case 8: return 'center';  // 居中对齐  
            case 3: case 6: case 9: return 'right';   // 右对齐
            default: return 'left';
        }
    }











    /**
     * 绘制TEXT实体
     */
    function drawText(entity, data) {
        var color = getColor(entity, data);

        if (!entity.text) return null;

        // 创建文本样式对象
        var style = {
            horizontalAlignment: getTextAlignment(entity.halign),
            verticalAlignment: getTextVerticalAlignment(entity.valign),
            textHeight: entity.textHeight || 10
        };

        // 创建实体对象
        var textEntity = {
            position: entity.startPoint || { x: 0, y: 0, z: 0 },
            rotation: entity.rotation
        };

        // 使用 Troika 创建文字
        return createTroikaText(entity.text, style, textEntity, color);
    }



    /**
     * 获取文本水平对齐方式
     */
    function getTextAlignment(halign) {
        switch (halign) {
            case 0: return 'left';     // Left
            case 1: return 'center';   // Center
            case 2: return 'right';    // Right
            default: return 'left';
        }
    }

    /**
     * 获取文本垂直对齐方式
     */
    function getTextVerticalAlignment(valign) {
        switch (valign) {
            case 0: return 'baseline'; // Baseline
            case 1: return 'bottom';   // Bottom
            case 2: return 'middle';   // Middle
            case 3: return 'top';      // Top
            default: return 'baseline';
        }
    }





    function drawSpline(entity, data) {
        var color = getColor(entity, data);

        var points = getBSplinePolyline(entity.controlPoints, entity.degreeOfSplineCurve, entity.knotValues, 100);

        var geometry = new THREE.BufferGeometry().setFromPoints(points);

        // 使用统一的材质创建函数
        var material = createLineMaterial(entity, data, color);
        var splineObject = new THREE.Line(geometry, material);

        // 如果是虚线材质，需要计算线段距离
        if (material instanceof THREE.LineDashedMaterial) {
            splineObject.computeLineDistances();
        }

        return splineObject;
    }

    /**
 * Interpolate a b-spline. The algorithm examins the knot vector
 * to create segments for interpolation. The parameterisation value
 * is re-normalised back to [0,1] as that is what the lib expects (
 * and t i de-normalised in the b-spline library)
 *
 * @param controlPoints the control points
 * @param degree the b-spline degree
 * @param knots the knot vector
 * @returns the polyline
 */
    function getBSplinePolyline(controlPoints, degree, knots, interpolationsPerSplineSegment, weights) {
        const polyline = []
        const controlPointsForLib = controlPoints.map(function (p) {
            return [p.x, p.y]
        })

        const segmentTs = [knots[degree]]
        const domain = [knots[degree], knots[knots.length - 1 - degree]]

        for (let k = degree + 1; k < knots.length - degree; ++k) {
            if (segmentTs[segmentTs.length - 1] !== knots[k]) {
                segmentTs.push(knots[k])
            }
        }

        interpolationsPerSplineSegment = interpolationsPerSplineSegment || 25
        for (let i = 1; i < segmentTs.length; ++i) {
            const uMin = segmentTs[i - 1]
            const uMax = segmentTs[i]
            for (let k = 0; k <= interpolationsPerSplineSegment; ++k) {
                const u = k / interpolationsPerSplineSegment * (uMax - uMin) + uMin
                // Clamp t to 0, 1 to handle numerical precision issues
                let t = (u - domain[0]) / (domain[1] - domain[0])
                t = Math.max(t, 0)
                t = Math.min(t, 1)
                const p = bSpline(t, degree, controlPointsForLib, knots, weights)
                polyline.push(new THREE.Vector2(p[0], p[1]));
            }
        }
        return polyline
    }

    function drawLine(entity, data) {
        let points = [];
        let color = getColor(entity, data);
        var material, vertex, startPoint, endPoint, bulge, i, line;

        if (!entity.vertices) return console.log('entity missing vertices.');

        for (i = 0; i < entity.vertices.length; i++) {

            if (entity.vertices[i].bulge) {
                bulge = entity.vertices[i].bulge;
                startPoint = entity.vertices[i];
                endPoint = i + 1 < entity.vertices.length ? entity.vertices[i + 1] : points[0];

                let bulgePoints = getBulgeCurvePoints(startPoint, endPoint, bulge);

                points.push.apply(points, bulgePoints);
            } else {
                vertex = entity.vertices[i];
                points.push(new THREE.Vector3(vertex.x, vertex.y, 0));
            }

        }
        if (entity.shape) points.push(points[0]);

        var geometry = new BufferGeometry().setFromPoints(points);

        // 性能优化：使用缓存的材质
        var lineType = entity.lineType || 'CONTINUOUS';

        if (lineType === 'CONTINUOUS') {
            // 实线使用缓存材质
            material = getCachedMaterial(color, 'line');
        } else {
            // 虚线等特殊线型仍使用原有方法
            material = createLineMaterial(entity, data, color);
        }

        line = new THREE.Line(geometry, material);

        // 如果是虚线材质，需要计算线段距离
        if (material instanceof THREE.LineDashedMaterial) {
            line.computeLineDistances();
        }

        return line;
    }


    /**
     * 计算多边形面积
     */
    function calculatePolygonArea(points) {
        if (points.length < 3) return 0;

        var area = 0;
        for (var i = 0; i < points.length - 1; i++) {
            var j = (i + 1) % points.length;
            area += points[i].x * points[j].y;
            area -= points[j].x * points[i].y;
        }
        return Math.abs(area) / 2;
    }

    /**
     * 计算边界框
     */
    function calculateBoundingBox(points) {
        if (points.length === 0) return { min: { x: 0, y: 0 }, max: { x: 0, y: 0 } };

        var minX = points[0].x, maxX = points[0].x;
        var minY = points[0].y, maxY = points[0].y;

        for (var i = 1; i < points.length; i++) {
            minX = Math.min(minX, points[i].x);
            maxX = Math.max(maxX, points[i].x);
            minY = Math.min(minY, points[i].y);
            maxY = Math.max(maxY, points[i].y);
        }

        return {
            min: { x: minX, y: minY },
            max: { x: maxX, y: maxY }
        };
    }

    /**
     * OCS到WCS坐标系转换 - 根据DXF规范实现
     * @param {Object} point - OCS坐标点 {x, y, z}
     * @param {Object} extrusion - 挤出方向向量 {x, y, z}
     * @returns {Object} WCS坐标点 {x, y, z}
     */
    function ocsToWcs(point, extrusion) {
        // 如果挤出方向是默认的(0,0,1)，不需要转换
        if (!extrusion || (extrusion.x === 0 && extrusion.y === 0 && extrusion.z === 1)) {
            return point;
        }

        // 规范化挤出向量作为Az（OCS的Z轴）
        var Az = normalizeVector(extrusion);
        
        // 使用任意轴算法计算Ax（OCS的X轴）
        var Ax;
        if (Math.abs(Az.x) < 1/64 && Math.abs(Az.y) < 1/64) {
            // 当Az接近Z轴时，使用Y轴叉乘
            Ax = normalizeVector(crossProduct({x: 0, y: 1, z: 0}, Az));
        } else {
            // 正常情况下使用Z轴叉乘
            Ax = normalizeVector(crossProduct({x: 0, y: 0, z: 1}, Az));
        }
        
        // 计算Ay（OCS的Y轴）= Az × Ax
        var Ay = normalizeVector(crossProduct(Az, Ax));
        
        // OCS到WCS转换
        return {
            x: point.x * Ax.x + point.y * Ay.x + point.z * Az.x,
            y: point.x * Ax.y + point.y * Ay.y + point.z * Az.y,
            z: point.x * Ax.z + point.y * Ay.z + point.z * Az.z
        };
    }

    /**
     * 向量叉乘
     */
    function crossProduct(a, b) {
        return {
            x: a.y * b.z - a.z * b.y,
            y: a.z * b.x - a.x * b.z,
            z: a.x * b.y - a.y * b.x
        };
    }

    /**
     * 向量规范化
     */
    function normalizeVector(v) {
        var length = Math.sqrt(v.x * v.x + v.y * v.y + v.z * v.z);
        if (length === 0) return {x: 0, y: 0, z: 1}; // 默认返回Z轴
        return {
            x: v.x / length,
            y: v.y / length,
            z: v.z / length
        };
    }

    /**
     * 将OCS中的角度转换为WCS中的角度
     * @param {number} angle - OCS中的角度（弧度）
     * @param {Object} extrusion - 挤出方向向量
     * @returns {number} WCS中的角度（弧度）
     */
    function ocsAngleToWcs(angle, extrusion) {
        // 如果是默认挤出方向，不需要转换
        if (!extrusion || (extrusion.x === 0 && extrusion.y === 0 && extrusion.z === 1)) {
            return angle;
        }

        // 将角度转换为单位向量
        var angleVector = {
            x: Math.cos(angle),
            y: Math.sin(angle),
            z: 0
        };

        // 转换向量从OCS到WCS
        var wcsVector = ocsToWcs(angleVector, extrusion);
        
        // 计算WCS中的角度（投影到XY平面）
        return Math.atan2(wcsVector.y, wcsVector.x);
    }

    function drawArc(entity, data) {
        var startAngle, endAngle;
        if (entity.type === 'CIRCLE') {
            startAngle = entity.startAngle || 0;
            endAngle = startAngle + 2 * Math.PI;
        } else {
            startAngle = entity.startAngle;
            endAngle = entity.endAngle;
        }

        // 处理OCS到WCS的坐标和角度转换
        var center = entity.center;
        var hasExtrusion = entity.extrusionDirectionX !== undefined || 
                          entity.extrusionDirectionY !== undefined || 
                          entity.extrusionDirectionZ !== undefined;

        if (hasExtrusion) {
            var extrusion = {
                x: entity.extrusionDirectionX || 0,
                y: entity.extrusionDirectionY || 0,
                z: entity.extrusionDirectionZ || 1
            };
            
            // 转换圆心坐标从OCS到WCS
            center = ocsToWcs(entity.center, extrusion);
            
            // 转换角度从OCS到WCS
            if (entity.type !== 'CIRCLE') {
                startAngle = ocsAngleToWcs(entity.startAngle, extrusion);
                endAngle = ocsAngleToWcs(entity.endAngle, extrusion);
                
                // 当extrusionDirectionZ为-1时，需要交换起始和结束角度
                if (extrusion.z < 0) {
                    var temp = startAngle;
                    startAngle = endAngle;
                    endAngle = temp;
                }
                
                // 确保绘制劣弧：如果角度差超过π，需要调整
                var angleDiff = endAngle - startAngle;
                
                // 规范化角度差到 [-π, π] 范围
                while (angleDiff > Math.PI) {
                    angleDiff -= 2 * Math.PI;
                }
                while (angleDiff < -Math.PI) {
                    angleDiff += 2 * Math.PI;
                }
                
                // 如果角度差为负，表示需要逆时针绘制
                if (angleDiff < 0) {
                    endAngle = startAngle + angleDiff + 2 * Math.PI;
                } else {
                    endAngle = startAngle + angleDiff;
                }
            }
            
            console.log('弧线转换:', {
                ocs: { 
                    center: entity.center, 
                    startAngle: (entity.startAngle * 180 / Math.PI).toFixed(1) + '°', 
                    endAngle: (entity.endAngle * 180 / Math.PI).toFixed(1) + '°'
                },
                wcs: { 
                    center: center, 
                    startAngle: (startAngle * 180 / Math.PI).toFixed(1) + '°', 
                    endAngle: (endAngle * 180 / Math.PI).toFixed(1) + '°',
                    arcLength: ((endAngle - startAngle) * 180 / Math.PI).toFixed(1) + '°'
                },
                extrusion: extrusion
            });
        }

        var curve = new THREE.ArcCurve(
            0, 0,
            entity.radius,
            startAngle,
            endAngle);

        var points = curve.getPoints(32);
        var geometry = new THREE.BufferGeometry().setFromPoints(points);

        // 性能优化：使用缓存的材质
        var color = getColor(entity, data);
        var lineType = entity.lineType || 'CONTINUOUS';
        var material;

        if (lineType === 'CONTINUOUS') {
            // 实线使用缓存材质
            material = getCachedMaterial(color, 'line');
        } else {
            // 虚线等特殊线型仍使用原有方法
            material = createLineMaterial(entity, data, color);
        }

        var arc = new THREE.Line(geometry, material);

        // 如果是虚线材质，需要计算线段距离
        if (material instanceof THREE.LineDashedMaterial) {
            arc.computeLineDistances();
        }

        arc.position.x = center.x;
        arc.position.y = center.y;
        arc.position.z = center.z;

        return arc;
    }


    /**
     * 绘制REGION实体
     */
    function drawRegion(entity, data) {
        // REGION实体通常表示填充区域
        if (entity.boundary && entity.boundary.length > 0) {
            var points = [];

            for (var i = 0; i < entity.boundary.length; i++) {
                var point = entity.boundary[i];
                points.push(new THREE.Vector3(point.x, point.y, point.z || 0));
            }

            if (points.length >= 3) {
                var color = getColor(entity, data);
                return createAdvancedPolygonFill(entity, points, color, data);
            }
        }

        return null;
    }

    function addTriangleFacingCamera(verts, p0, p1, p2) {
        // Calculate which direction the points are facing (clockwise or counter-clockwise)
        var vector1 = new Vector3();
        var vector2 = new Vector3();
        vector1.subVectors(p1, p0);
        vector2.subVectors(p2, p0);
        vector1.cross(vector2);

        var v0 = new Vector3(p0.x, p0.y, p0.z);
        var v1 = new Vector3(p1.x, p1.y, p1.z);
        var v2 = new Vector3(p2.x, p2.y, p2.z);

        // If z < 0 then we must draw these in reverse order
        if (vector1.z < 0) {
            verts.push(v2, v1, v0);
        } else {
            verts.push(v0, v1, v2);
        }
    }

    function drawSolid(entity, data) {
        var color = getColor(entity, data);

        // 改进的SOLID处理，参考three-dxf-viewer的实现
        if (entity.corners && entity.corners.length >= 3) {
            try {
                // 将corners转换为Vector3点
                var points = entity.corners.map(function(corner) {
                    return new THREE.Vector3(corner.x, corner.y, corner.z || 0);
                });

                // 重新排列点的顺序（参考three-dxf-viewer）
                points.splice(0, 0, points.pop());

                // 创建形状
                var shape = new THREE.Shape();
                shape.moveTo(points[0].x, points[0].y);

                for (var i = 1; i < points.length; i++) {
                    shape.lineTo(points[i].x, points[i].y);
                }

                var geometry = new THREE.ShapeGeometry(shape);

                // 性能优化：使用缓存的材质
                var material = getCachedMaterial(color, 'mesh');
                material.transparent = true;
                material.opacity = 0.8;

                var mesh = new THREE.Mesh(geometry, material);
                mesh.position.z = -0.1;
                return mesh;

            } catch (error) {
                console.warn('Failed to create solid fill:', error);
            }
        }

        // 回退到原始实现
        var material, verts,
            geometry = new THREE.BufferGeometry();

        var points = entity.points;
        verts = [];

        // 确保有足够的点来创建三角形
        if (points && points.length >= 3) {
            addTriangleFacingCamera(verts, points[0], points[1], points[2]);

            // 如果有第四个点，创建第二个三角形
            if (points.length >= 4) {
                addTriangleFacingCamera(verts, points[1], points[2], points[3]);
            }
        }

        // 性能优化：使用缓存的材质
        material = getCachedMaterial(color, 'mesh');
        material.transparent = true;
        material.opacity = 0.8;

        geometry.setFromPoints(verts);

        var mesh = new THREE.Mesh(geometry, material);
        mesh.position.z = -0.1; // 将SOLID填充放在下层
        return mesh;
    }

    /**
     * 绘制HATCH填充实体 - 简化实现
     */
    function drawHatch(entity, data) {
        if (!entity.boundaryPaths || entity.boundaryPaths.length === 0) {
            return null;
        }

        var color = getColor(entity, data);
        var group = new THREE.Object3D();

        try {
            // 处理每个边界路径
            for (var i = 0; i < entity.boundaryPaths.length; i++) {
                var boundaryPath = entity.boundaryPaths[i];
                var points = extractSimpleBoundaryPoints(boundaryPath);
                
                if (points && points.length >= 3) {
                    var mesh = createSimpleHatchFill(entity, points, color);
                    if (mesh) {
                        group.add(mesh);
                    }
                }
            }

            return group.children.length > 0 ? group : null;

        } catch (error) {
            return null;
        }
    }

    /**
     * 简化的边界点提取
     */
    function extractSimpleBoundaryPoints(boundaryPath) {
        var points = [];
        
        if (boundaryPath.pathType === 'POLYLINE' && boundaryPath.vertices) {
            for (var i = 0; i < boundaryPath.vertices.length; i++) {
                var vertex = boundaryPath.vertices[i];
                if (typeof vertex.x === 'number' && typeof vertex.y === 'number') {
                    points.push(new THREE.Vector3(vertex.x, vertex.y, vertex.z || 0));
                }
            }
        } else if (boundaryPath.pathType === 'EDGE' && boundaryPath.edges) {
            for (var i = 0; i < boundaryPath.edges.length; i++) {
                var edge = boundaryPath.edges[i];
                if (edge.type === 'LINE') {
                    if (i === 0) {
                        points.push(new THREE.Vector3(edge.start.x, edge.start.y, 0));
                    }
                    points.push(new THREE.Vector3(edge.end.x, edge.end.y, 0));
                }
            }
        }
        
        return points;
    }

    /**
     * 创建简单的HATCH填充
     */
    function createSimpleHatchFill(entity, points, color) {
        try {
            // 创建形状
            var shape = new THREE.Shape();
            shape.moveTo(points[0].x, points[0].y);
            
            for (var i = 1; i < points.length; i++) {
                shape.lineTo(points[i].x, points[i].y);
            }
            
            var geometry = new THREE.ShapeGeometry(shape);
            var material = new THREE.MeshBasicMaterial({
                color: new THREE.Color(color),
                side: THREE.DoubleSide,
                transparent: true,
                opacity: entity.solidFill ? 0.8 : 0.3
            });
            
            var mesh = new THREE.Mesh(geometry, material);
            mesh.position.z = -0.1;
            return mesh;
            
        } catch (error) {
            return null;
        }
    }



    



    function drawPoint(entity, data) {
        var geometry, material, point;

        geometry = new THREE.BufferGeometry();
        geometry.setAttribute('position', new Float32BufferAttribute([entity.position.x, entity.position.y, entity.position.z], 3));

        var color = getColor(entity, data);

        // 性能优化：使用缓存的点材质
        material = getCachedMaterial(color, 'point');
        point = new THREE.Points(geometry, material);
        return point;
    }

    function drawDimension(entity, data) {
        var block = data.blocks[entity.block];

        if (!block || !block.entities) return null;

        var group = new THREE.Object3D();
        // if(entity.anchorPoint) {
        //     group.position.x = entity.anchorPoint.x;
        //     group.position.y = entity.anchorPoint.y;
        //     group.position.z = entity.anchorPoint.z;
        // }

        // 更新变换矩阵，确保在处理子实体前矩阵是最新的
        group.updateMatrixWorld(true);

        for (var i = 0; i < block.entities.length; i++) {
            var childEntity = drawEntity(block.entities[i], data, group);
            if (childEntity) group.add(childEntity);
        }

        return group;
    }

        function drawBlock(entity, data) {
        var block = data.blocks[entity.name];

        if (!block || !block.entities) return null;

        var group = new THREE.Object3D();

        // 设置缩放因子（处理缺少缩放因子的情况）
        if (entity.xScale !== undefined) {
            group.scale.x = entity.xScale;
        } else {
            group.scale.x = 1.0; // 默认值
        }
        
        if (entity.yScale !== undefined) {
            group.scale.y = entity.yScale;
        } else {
            group.scale.y = 1.0; // 默认值
        }

        if (entity.zScale !== undefined) {
            group.scale.z = entity.zScale;
        } else {
            group.scale.z = 1.0; // 默认值
        }

        // 设置旋转
        if (entity.rotation) {
            group.rotation.z = entity.rotation * Math.PI / 180;
        }

        // 设置块插入位置
        if (entity.position) {
            group.position.x = entity.position.x;
            group.position.y = entity.position.y;
            group.position.z = entity.position.z;
        }

        // 处理块基点偏移
        var blockBasePoint = block.position || { x: 0, y: 0, z: 0 };

        
        // 更新变换矩阵，确保在处理子实体前矩阵是最新的
        group.updateMatrixWorld(true);

        // 简单处理：根据您的分析，尝试10倍缩放修正
        for (var i = 0; i < block.entities.length; i++) {
            var originalEntity = block.entities[i];
            // 块内实体继承父实体的图层，如果子实体没有指定图层的话
            if (!originalEntity.layer && entity.layer) {
                originalEntity = Object.assign({}, originalEntity);
                originalEntity.layer = entity.layer;
            }

            // 其他块正常处理
                var childEntity = drawEntity(originalEntity, data, group);
            if (childEntity) {
                // 应用块基点偏移
                if (blockBasePoint.x !== 0 || blockBasePoint.y !== 0 || blockBasePoint.z !== 0) {
                    childEntity.position.x -= blockBasePoint.x;
                    childEntity.position.y -= blockBasePoint.y;
                    childEntity.position.z -= blockBasePoint.z;
                }
                group.add(childEntity);
            }
        }

        return group;
    }

    function getColor(entity, data) {
        var color = 0xffffff; //default
        if (entity.color) color = entity.color;
        else if (data.tables && data.tables.layer && data.tables.layer.layers[entity.layer])
            color = data.tables.layer.layers[entity.layer].color;

        if (color == null) {
            color = 0xffffff;
        }

        // 将黑色转换为白色
        // if (color === 0xffffff) {
        //     color = 0x000000;
        // }

        return color;
    }

    /**
     * 创建线条材质，支持虚线
     */
    function createLineMaterial(entity, data, color) {
        var lineType;

        // 获取线型信息
        if (entity.lineType && data.tables && data.tables.lineType) {
            lineType = data.tables.lineType.lineTypes[entity.lineType];
        }

        // 如果有线型模式且不为空，创建虚线材质
        if (lineType && lineType.pattern && lineType.pattern.length > 0) {
            var dashSize = 4;
            var gapSize = 4;

            // 从线型模式中提取虚线参数
            if (lineType.pattern.length >= 2) {
                dashSize = Math.abs(lineType.pattern[0]) || 4;
                gapSize = Math.abs(lineType.pattern[1]) || 4;
            }

            return new THREE.LineDashedMaterial({
                color: color,
                dashSize: dashSize,
                gapSize: gapSize
            });
        }

        // 默认实线材质
        return new THREE.LineBasicMaterial({
            color: color
        });
    }

    function createLineTypeShaders(data) {
        var ltype, type;
        if (!data.tables || !data.tables.lineType) return;
        var ltypes = data.tables.lineType.lineTypes;

        for (type in ltypes) {
            ltype = ltypes[type];
            if (!ltype.pattern) continue;
            ltype.material = createDashedLineShader(ltype.pattern);
        }
    }

    function createDashedLineShader(pattern) {
        var i,
            dashedLineShader = {},
            totalLength = 0.0;

        for (i = 0; i < pattern.length; i++) {
            totalLength += Math.abs(pattern[i]);
        }

        dashedLineShader.uniforms = THREE.UniformsUtils.merge([

            THREE.UniformsLib['common'],
            THREE.UniformsLib['fog'],

            {
                'pattern': { type: 'fv1', value: pattern },
                'patternLength': { type: 'f', value: totalLength }
            }

        ]);

        dashedLineShader.vertexShader = [
            'attribute float lineDistance;',

            'varying float vLineDistance;',

            THREE.ShaderChunk['color_pars_vertex'],

            'void main() {',

            THREE.ShaderChunk['color_vertex'],

            'vLineDistance = lineDistance;',

            'gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );',

            '}'
        ].join('\n');

        dashedLineShader.fragmentShader = [
            'uniform vec3 diffuse;',
            'uniform float opacity;',

            'uniform float pattern[' + pattern.length + '];',
            'uniform float patternLength;',

            'varying float vLineDistance;',

            THREE.ShaderChunk['color_pars_fragment'],
            THREE.ShaderChunk['fog_pars_fragment'],

            'void main() {',

            'float pos = mod(vLineDistance, patternLength);',

            'for ( int i = 0; i < ' + pattern.length + '; i++ ) {',
            'pos = pos - abs(pattern[i]);',
            'if( pos < 0.0 ) {',
            'if( pattern[i] > 0.0 ) {',
            'gl_FragColor = vec4(1.0, 0.0, 0.0, opacity );',
            'break;',
            '}',
            'discard;',
            '}',

            '}',

            THREE.ShaderChunk['color_fragment'],
            THREE.ShaderChunk['fog_fragment'],

            '}'
        ].join('\n');

        return dashedLineShader;
    }

    function findExtents(scene) {
        for (var child of scene.children) {
            var minX, maxX, minY, maxY;
            if (child.position) {
                minX = Math.min(child.position.x, minX);
                minY = Math.min(child.position.y, minY);
                maxX = Math.max(child.position.x, maxX);
                maxY = Math.max(child.position.y, maxY);
            }
        }

        return { min: { x: minX, y: minY }, max: { x: maxX, y: maxY } };
    }

    /**
     * 初始化图层信息
     */
    function initializeLayers(data) {
        // 从图层表中预加载已定义的图层
        if (data.tables && data.tables.layer && data.tables.layer.layers) {
            var layers = data.tables.layer.layers;
            for (var layerName in layers) {
                var layerInfo = layers[layerName];
                createLayer(layerName, {
                    color: layerInfo.color || 0xffffff,
                    visible: layerInfo.visible !== false,
                    frozen: layerInfo.frozen === true,
                    description: layerInfo.description || ''
                });
            }
        }
        
        // 确保默认图层"0"存在
        if (!layerMap.has('0')) {
            createLayer('0', {
                color: 0xffffff,
                visible: true,
                frozen: false,
                description: '默认图层'
            });
        }
    }

    /**
     * 创建图层
     */
    function createLayer(layerName, layerInfo) {
        if (!layerMap.has(layerName)) {
            layerMap.set(layerName, {
                name: layerName,
                color: layerInfo.color || 0xffffff,
                visible: layerInfo.visible !== false,
                frozen: layerInfo.frozen === false,
                description: layerInfo.description || '',
                entityCount: 0
            });
            
            // 创建图层Group并添加到场景
            var layerGroup = new THREE.Group();
            layerGroup.name = layerName;
            layerGroup.visible = layerInfo.visible !== false;
            layerGroups.set(layerName, layerGroup);
            scene.add(layerGroup);
            
        }
    }

    /**
     * 将实体添加到指定图层
     */
    function addEntityToLayer(entity, layerName) {
        if (!layerMap.has(layerName)) {
            createLayer(layerName, {
                color: 0xffffff,
                visible: true,
                frozen: false,
                description: '自动创建的图层'
            });
        }
        
        var layerGroup = layerGroups.get(layerName);
        if (layerGroup) {
            layerGroup.add(entity);
            
            var layerInfo = layerMap.get(layerName);
            layerInfo.entityCount++;
        } else {
            console.error('❌ 无法获取图层组:', layerName);
        }
    }

    /**
     * 获取所有图层信息
     */
    this.getLayers = function() {
        var layersArray = [];
        layerMap.forEach(function(layerInfo, layerName) {
            layersArray.push({
                name: layerName,
                color: layerInfo.color,
                visible: layerInfo.visible,
                frozen: layerInfo.frozen,
                description: layerInfo.description,
                entityCount: layerInfo.entityCount
            });
        });
        return layersArray;
    }

    /**
     * 获取图层Map
     */
    this.getLayerMap = function() {
        return layerMap;
    }

    /**
     * 显示指定图层
     */
    this.showLayer = function(layerName) {
        var layerGroup = layerGroups.get(layerName);
        var layerInfo = layerMap.get(layerName);
        
        if (layerGroup && layerInfo) {
            layerGroup.visible = true;
            layerInfo.visible = true;
            this.render();
            return true;
        }
        return false;
    }

    /**
     * 隐藏指定图层
     */
    this.hideLayer = function(layerName) {
        var layerGroup = layerGroups.get(layerName);
        var layerInfo = layerMap.get(layerName);
        
        if (layerGroup && layerInfo) {
            layerGroup.visible = false;
            layerInfo.visible = false;
            this.render();
            return true;
        }
        return false;
    }

    /**
     * 切换图层显示状态
     */
    this.toggleLayer = function(layerName) {
        var layerInfo = layerMap.get(layerName);
        if (layerInfo) {
            if (layerInfo.visible) {
                this.hideLayer(layerName);
            } else {
                this.showLayer(layerName);
            }
            return layerInfo.visible;
        }
        return false;
    }

    /**
     * 显示所有图层
     */
    this.showAllLayers = function() {
        layerGroups.forEach(function(layerGroup, layerName) {
            layerGroup.visible = true;
            var layerInfo = layerMap.get(layerName);
            if (layerInfo) layerInfo.visible = true;
        });
        this.render();
    }

    /**
     * 隐藏所有图层
     */
    this.hideAllLayers = function() {
        layerGroups.forEach(function(layerGroup, layerName) {
            layerGroup.visible = false;
            var layerInfo = layerMap.get(layerName);
            if (layerInfo) layerInfo.visible = false;
        });
        this.render();
    }

    /**
     * 获取指定图层的Group对象
     */
    this.getLayerGroup = function(layerName) {
        return layerGroups.get(layerName);
    }

    /**
     * 检查图层是否存在
     */
    this.hasLayer = function(layerName) {
        return layerMap.has(layerName);
    }

    /**
     * 获取图层统计信息
     */
    this.getLayerStats = function() {
        return {
            totalLayers: layerMap.size,
            visibleLayers: Array.from(layerMap.values()).filter(layer => layer.visible).length,
            totalEntities: Array.from(layerMap.values()).reduce((sum, layer) => sum + layer.entityCount, 0)
        };
    }

    /**
     * 清理所有不受图层控制的对象（临时解决方案）
     */
    this.cleanupUnmanagedObjects = function() {
        var removedCount = 0;
        var sceneChildren = scene.children.slice();
        
        sceneChildren.forEach(function(child) {
            var isLayerGroup = false;
            layerGroups.forEach(function(layerGroup) {
                if (child === layerGroup) {
                    isLayerGroup = true;
                }
            });
            
            // 如果不是图层组，则移除（这些可能是直接添加的点或其他元素）
            if (!isLayerGroup) {
                scene.remove(child);
                removedCount++;
                console.log(' 清理未受管理的对象:', {
                    type: child.type,
                    position: child.position,
                    hasGeometry: !!child.geometry
                });
                
                // 清理几何体和材质
                if (child.geometry) {
                    child.geometry.dispose();
                }
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(material => material.dispose());
                    } else {
                        child.material.dispose();
                    }
                }
            }
        });
        this.render();
        return removedCount;
    }

    /**
     * 检查并存储封闭图形
     */
    function checkAndStoreClosedShape(entity, data, parentGroup) {

        if (!entity.vertices || entity.vertices.length < 3) {
            return;
        }

        var isClosed = isClosedShape(entity);
        if (!isClosed) {
            return;
        }

        var points = extractShapePoints(entity, parentGroup);
        if (points.length < 3) {
            return;
        }

        var shapeInfo = {
            id: 'dxf_shape_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
            type: entity.type,
            layer: entity.layer || '0',
            points: points,
            entity: entity,
            area: calculateShapeArea(points),
            bounds: calculateShapeBounds(points)
        };

        closedShapes.push(shapeInfo);
    }

    /**
     * 判断实体是否为封闭图形
     */
    function isClosedShape(entity) {
        if (!entity.vertices || entity.vertices.length < 3) {
            return false;
        }

        // 检查shape标志
        if (entity.shape === true || entity.closed === true) {
            return true;
        }

        // 检查首尾点是否重合
        var firstPoint = entity.vertices[0];
        var lastPoint = entity.vertices[entity.vertices.length - 1];
        
        if (firstPoint && lastPoint) {
            var tolerance = 1.0; // 增加容差到1.0单位
            var dx = Math.abs(firstPoint.x - lastPoint.x);
            var dy = Math.abs(firstPoint.y - lastPoint.y);
            
            
            if (dx < tolerance && dy < tolerance) {
                return true;
            }
        }

        // 对于LWPOLYLINE，检查额外的封闭标志
        if (entity.type === 'LWPOLYLINE') {
            // 某些DXF文件可能使用不同的封闭标志
            if (entity.flag && (entity.flag & 1)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 提取图形的顶点坐标
     */
    function extractShapePoints(entity, parentGroup) {
        var points = [];
        
        for (var i = 0; i < entity.vertices.length; i++) {
            var vertex = entity.vertices[i];
            if (vertex && typeof vertex.x === 'number' && typeof vertex.y === 'number') {
                var point = { x: vertex.x, y: vertex.y, z: vertex.z || 0 };
                
                // 如果有父组变换，应用变换
                if (parentGroup) {
                    var tempVector = new THREE.Vector3(point.x, point.y, point.z);
                    tempVector.applyMatrix4(parentGroup.matrixWorld);
                    point.x = tempVector.x;
                    point.y = tempVector.y;
                    point.z = tempVector.z;
                }
                
                points.push(point);
            }
        }
        
        return points;
    }

    /**
     * 计算图形面积
     */
    function calculateShapeArea(points) {
        if (points.length < 3) return 0;
        
        var area = 0;
        for (var i = 0; i < points.length; i++) {
            var j = (i + 1) % points.length;
            area += points[i].x * points[j].y;
            area -= points[j].x * points[i].y;
        }
        return Math.abs(area) / 2;
    }

    /**
     * 计算图形边界框
     */
    function calculateShapeBounds(points) {
        if (points.length === 0) return { min: { x: 0, y: 0 }, max: { x: 0, y: 0 } };
        
        var minX = points[0].x, maxX = points[0].x;
        var minY = points[0].y, maxY = points[0].y;
        
        for (var i = 1; i < points.length; i++) {
            minX = Math.min(minX, points[i].x);
            maxX = Math.max(maxX, points[i].x);
            minY = Math.min(minY, points[i].y);
            maxY = Math.max(maxY, points[i].y);
        }
        
        return {
            min: { x: minX, y: minY },
            max: { x: maxX, y: maxY }
        };
    }

    /**
     * 点在多边形内检测（射线法）
     */
    function pointInPolygon(point, polygonPoints) {
        var x = point.x, y = point.y;
        var inside = false;
        
        for (var i = 0, j = polygonPoints.length - 1; i < polygonPoints.length; j = i++) {
            var xi = polygonPoints[i].x, yi = polygonPoints[i].y;
            var xj = polygonPoints[j].x, yj = polygonPoints[j].y;
            
            if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
                inside = !inside;
            }
        }
        
        return inside;
    }

    /**
     * 获取所有封闭图形
     */
    this.getClosedShapes = function() {
        return closedShapes.slice(); // 返回副本
    }

    /**
     * 根据点击位置查找包含该点的封闭图形
     */
    this.findShapeByPoint = function(point) {
        if (closedShapes.length === 0) return null;

        // 计算所有图形的总边界框
        var totalBounds = calculateTotalBounds();
        var totalArea = (totalBounds.max.x - totalBounds.min.x) * (totalBounds.max.y - totalBounds.min.y);
        
        // 智能计算过滤阈值
        var filterThreshold = calculateSmartFilterThreshold(totalArea);
        var maxAllowedArea = totalArea * filterThreshold;

        // 显示所有图形的面积信息
        var sortedByArea = closedShapes.slice().sort(function(a, b) { return b.area - a.area; });
        // console.log(' 所有图形面积分布:');
        for (var k = 0; k < Math.min(10, sortedByArea.length); k++) {
            var shape = sortedByArea[k];
            var ratio = (shape.area / totalArea * 100).toFixed(1);
            console.log(`  ${k+1}. ${shape.type} (${shape.layer}) - 面积:${Math.round(shape.area)} (${ratio}%) ${shape.area > maxAllowedArea ? '过大' : '符合'}`);
        }

        // 过滤掉面积过大的图形
        var filteredShapes = closedShapes.filter(function(shape) {
            var isAreaValid = shape.area <= maxAllowedArea;
            if (!isAreaValid) {
            }
            return isAreaValid;
        });

        console.log(' 面积过滤结果:', {
            原始图形数: closedShapes.length,
            过滤后图形数: filteredShapes.length,
            过滤掉: closedShapes.length - filteredShapes.length
        });

        // 按面积从小到大排序，优先返回最小的包含图形
        var sortedShapes = filteredShapes.slice().sort(function(a, b) {
            return a.area - b.area;
        });
        
        var foundShapes = [];
        
        for (var i = 0; i < sortedShapes.length; i++) {
            var shape = sortedShapes[i];
            var isInside = pointInPolygon(point, shape.points);
            
            if (isInside) {
                foundShapes.push(shape);
                return shape; // 立即返回第一个（最小的）匹配图形
            }
        }
        
        console.log(' 未找到符合条件的封闭图形');
        return null;
    }

    /**
     * 智能计算过滤阈值
     */
    function calculateSmartFilterThreshold(totalArea) {
        if (closedShapes.length === 0) return 0.3;

        // 计算图形面积的统计信息
        var areas = closedShapes.map(function(shape) { return shape.area; });
        areas.sort(function(a, b) { return a - b; });

        var median = areas[Math.floor(areas.length / 2)];
        var mean = areas.reduce(function(sum, area) { return sum + area; }, 0) / areas.length;
        var maxArea = Math.max.apply(Math, areas);

        var medianRatio = median / totalArea;
        var meanRatio = mean / totalArea;
        var maxRatio = maxArea / totalArea;

        console.log(' 图形面积统计:', {
            中位数面积: Math.round(median),
            平均面积: Math.round(mean),
            最大面积: Math.round(maxArea),
            中位数占比: (medianRatio * 100).toFixed(2) + '%',
            平均占比: (meanRatio * 100).toFixed(2) + '%',
            最大占比: (maxRatio * 100).toFixed(2) + '%'
        });

        // 智能阈值策略
        var threshold;
        if (maxRatio > 0.9) {
            // 如果最大图形占比超过90%，使用极低阈值
            threshold = Math.max(0.05, medianRatio * 20);
        } else if (maxRatio > 0.5) {
            // 如果最大图形占比在50%-90%，使用中等阈值
            threshold = Math.max(0.2, meanRatio * 10);
        } else {
            // 如果最大图形占比在50%以下，使用宽松阈值
            threshold = 0.6;
        }

        // 确保阈值在合理范围内
        threshold = Math.max(0.05, Math.min(0.8, threshold));

        return threshold;
    }

    /**
     * 计算所有图形的总边界框 - 先排除大图形再计算边界
     */
    function calculateTotalBounds() {
        if (closedShapes.length === 0) {
            return { min: { x: 0, y: 0 }, max: { x: 0, y: 0 } };
        }

        // 第一步：计算面积统计，识别异常大的图形
        var areas = closedShapes.map(function(shape) { return shape.area; });
        areas.sort(function(a, b) { return a - b; });
        
        var median = areas[Math.floor(areas.length / 2)];
        var q3 = areas[Math.floor(areas.length * 0.75)]; // 75分位数
        var mean = areas.reduce(function(sum, area) { return sum + area; }, 0) / areas.length;
        
        // 设置大图形过滤阈值：只排除真正的异常大图形（如图框）
        // 超过中位数200倍，或超过75分位数100倍的才认为是异常大图形
        var largeShapeThreshold = Math.max(median * 200, q3 * 100);
        
        // console.log(' 面积统计分析:', {
        //     总图形数: closedShapes.length,
        //     中位数面积: Math.round(median),
        //     '75分位数面积': Math.round(q3),
        //     平均面积: Math.round(mean),
        //     大图形阈值: Math.round(largeShapeThreshold),
        //     阈值说明: '中位数200倍或75分位数100倍'
        // });

        // 第二步：过滤掉异常大的图形
        var normalShapes = closedShapes.filter(function(shape) {
            var isTooLarge = shape.area > largeShapeThreshold;
            return !isTooLarge;
        });

        // 第三步：如果过滤后图形太少，使用更宽松的策略
        if (normalShapes.length < closedShapes.length * 0.2) {
            // 只排除超过中位数500倍的极端大图形（如整个图纸边框）
            normalShapes = closedShapes.filter(function(shape) {
                return shape.area <= median * 500;
            });
        }

        // 第四步：用过滤后的图形计算边界
        if (normalShapes.length === 0) {
            console.warn(' 没有有效图形用于边界计算');
            return { min: { x: 0, y: 0 }, max: { x: 0, y: 0 } };
        }

        var minX = Infinity, maxX = -Infinity;
        var minY = Infinity, maxY = -Infinity;
        var totalPoints = 0;

        normalShapes.forEach(function(shape) {
            if (shape.points && shape.points.length > 0) {
                shape.points.forEach(function(point) {
                    minX = Math.min(minX, point.x);
                    maxX = Math.max(maxX, point.x);
                    minY = Math.min(minY, point.y);
                    maxY = Math.max(maxY, point.y);
                    totalPoints++;
                });
            }
        });

        var result = {
            min: { x: minX, y: minY },
            max: { x: maxX, y: maxY }
        };

        console.log(' 最终边界结果:', {
            使用图形数: normalShapes.length,
            总点数: totalPoints,
            左下角: `(${minX.toFixed(1)}, ${minY.toFixed(1)})`,
            右上角: `(${maxX.toFixed(1)}, ${maxY.toFixed(1)})`,
            宽度: (maxX - minX).toFixed(1),
            高度: (maxY - minY).toFixed(1),
            边界面积: Math.round((maxX - minX) * (maxY - minY))
        });

        return result;
    }

    /**
     * 获取封闭图形统计信息
     */
    this.getClosedShapeStats = function() {
        if (closedShapes.length === 0) {
            return {
                totalShapes: 0,
                filteredShapes: 0,
                largeShapesFiltered: 0,
                averageArea: 0,
                totalArea: 0,
                maxAllowedArea: 0
            };
        }

        var totalBounds = calculateTotalBounds();
        var totalArea = (totalBounds.max.x - totalBounds.min.x) * (totalBounds.max.y - totalBounds.min.y);
        var maxAllowedArea = totalArea * 0.5;

        var filteredShapes = closedShapes.filter(function(shape) {
            return shape.area <= maxAllowedArea;
        });

        var averageArea = filteredShapes.length > 0 ? 
            filteredShapes.reduce(function(sum, shape) { return sum + shape.area; }, 0) / filteredShapes.length : 0;

        return {
            totalShapes: closedShapes.length,
            filteredShapes: filteredShapes.length,
            largeShapesFiltered: closedShapes.length - filteredShapes.length,
            averageArea: Math.round(averageArea),
            totalArea: Math.round(totalArea),
            maxAllowedArea: Math.round(maxAllowedArea),
            filterRatio: (filteredShapes.length / closedShapes.length * 100).toFixed(1) + '%'
        };
    }

    /**
     * 检查HATCH实体是否可以作为封闭图形
     */
    function checkHatchAsClosedShape(entity, data, parentGroup) {

        if (!entity.boundaryPaths || entity.boundaryPaths.length === 0) {
            return;
        }

        // 处理每个边界路径
        for (var pathIndex = 0; pathIndex < entity.boundaryPaths.length; pathIndex++) {
            var boundaryPath = entity.boundaryPaths[pathIndex];
            var points = [];

            if (boundaryPath.pathType === 'POLYLINE' && boundaryPath.vertices) {
                // 处理多段线边界
                for (var i = 0; i < boundaryPath.vertices.length; i++) {
                    var vertex = boundaryPath.vertices[i];
                    if (vertex && typeof vertex.x === 'number' && typeof vertex.y === 'number') {
                        points.push({ x: vertex.x, y: vertex.y, z: vertex.z || 0 });
                    }
                }
            } else if (boundaryPath.pathType === 'EDGE' && boundaryPath.edges) {
                // 处理边界edge
                for (var i = 0; i < boundaryPath.edges.length; i++) {
                    var edge = boundaryPath.edges[i];
                    
                    if (edge.type === 'LINE') {
                        if (i === 0 || !points.length) {
                            points.push({ x: edge.start.x, y: edge.start.y, z: 0 });
                        }
                        points.push({ x: edge.end.x, y: edge.end.y, z: 0 });
                    } else if (edge.type === 'ARC') {
                        // 处理弧形边界
                        var arcPoints = generateArcPoints(edge);
                        if (i === 0 || !points.length) {
                            points.push.apply(points, arcPoints);
                        } else {
                            // 跳过第一个点避免重复
                            points.push.apply(points, arcPoints.slice(1));
                        }
                    } else if (edge.type === 'ELLIPSE') {
                        // 处理椭圆边界
                        var ellipsePoints = generateEllipsePoints(edge);
                        if (i === 0 || !points.length) {
                            points.push.apply(points, ellipsePoints);
                        } else {
                            points.push.apply(points, ellipsePoints.slice(1));
                        }
                    }
                }
            } else if (boundaryPath.vertices) {
                // 直接处理顶点（备用方案）
                for (var i = 0; i < boundaryPath.vertices.length; i++) {
                    var vertex = boundaryPath.vertices[i];
                    if (vertex && typeof vertex.x === 'number' && typeof vertex.y === 'number') {
                        points.push({ x: vertex.x, y: vertex.y, z: vertex.z || 0 });
                    }
                }
            }

            if (points.length >= 3) {
                // 应用父组变换（如果存在）
                if (parentGroup) {
                    points = points.map(function(point) {
                        var tempVector = new THREE.Vector3(point.x, point.y, point.z);
                        tempVector.applyMatrix4(parentGroup.matrixWorld);
                        return { x: tempVector.x, y: tempVector.y, z: tempVector.z };
                    });
                }

                var shapeInfo = {
                    id: 'dxf_hatch_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
                    type: 'HATCH',
                    layer: entity.layer || '0',
                    points: points,
                    entity: entity,
                    area: calculateShapeArea(points),
                    bounds: calculateShapeBounds(points)
                };

                closedShapes.push(shapeInfo);
            } else {
            }
        }
    }

    /**
     * 生成弧形边界点
     */
    function generateArcPoints(arcEdge) {
        var points = [];
        var segments = 16; // 弧形分段数
        
        var center = arcEdge.center;
        var radius = arcEdge.radius;
        var startAngle = arcEdge.startAngle;
        var endAngle = arcEdge.endAngle;
        
        var angleStep = (endAngle - startAngle) / segments;
        
        for (var i = 0; i <= segments; i++) {
            var angle = startAngle + i * angleStep;
            var x = center.x + radius * Math.cos(angle);
            var y = center.y + radius * Math.sin(angle);
            points.push({ x: x, y: y, z: 0 });
        }
        
        return points;
    }

    /**
     * 生成椭圆边界点
     */
    function generateEllipsePoints(ellipseEdge) {
        var points = [];
        var segments = 32; // 椭圆分段数
        
        // 这里需要根据椭圆的具体参数来生成点
        // 简化处理，将椭圆当作圆处理
        var center = ellipseEdge.center || { x: 0, y: 0 };
        var radius = ellipseEdge.majorRadius || ellipseEdge.radius || 1;
        
        var angleStep = (2 * Math.PI) / segments;
        
        for (var i = 0; i < segments; i++) {
            var angle = i * angleStep;
            var x = center.x + radius * Math.cos(angle);
            var y = center.y + radius * Math.sin(angle);
            points.push({ x: x, y: y, z: 0 });
        }
        
        return points;
    }

    /**
     * 检查REGION实体是否可以作为封闭图形
     */
    function checkRegionAsClosedShape(entity, data, parentGroup) {

        if (!entity.boundary || entity.boundary.length < 3) {
            return;
        }

        var points = [];
        for (var i = 0; i < entity.boundary.length; i++) {
            var point = entity.boundary[i];
            points.push({ x: point.x, y: point.y, z: point.z || 0 });
        }

        var shapeInfo = {
            id: 'dxf_region_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
            type: 'REGION',
            layer: entity.layer || '0',
            points: points,
            entity: entity,
            area: calculateShapeArea(points),
            bounds: calculateShapeBounds(points)
        };

        closedShapes.push(shapeInfo);
    }

    /**
     * 检查CIRCLE实体是否可以作为封闭图形
     */
    function checkCircleAsClosedShape(entity, data, parentGroup) {
        if (!entity.center || !entity.radius) {
            return;
        }

        // 生成圆形的近似多边形点
        var segments = 32; // 圆形分段数
        var points = [];
        var angleStep = (2 * Math.PI) / segments;

        for (var i = 0; i < segments; i++) {
            var angle = i * angleStep;
            var x = entity.center.x + entity.radius * Math.cos(angle);
            var y = entity.center.y + entity.radius * Math.sin(angle);
            points.push({ x: x, y: y, z: entity.center.z || 0 });
        }

        // 应用父组变换（如果存在）
        if (parentGroup) {
            points = points.map(function(point) {
                var tempVector = new THREE.Vector3(point.x, point.y, point.z);
                tempVector.applyMatrix4(parentGroup.matrixWorld);
                return { x: tempVector.x, y: tempVector.y, z: tempVector.z };
            });
        }

        var area = Math.PI * entity.radius * entity.radius; // 圆的面积

        var shapeInfo = {
            id: 'dxf_circle_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
            type: 'CIRCLE',
            layer: entity.layer || '0',
            points: points,
            entity: entity,
            area: area,
            bounds: {
                min: { 
                    x: entity.center.x - entity.radius, 
                    y: entity.center.y - entity.radius 
                },
                max: { 
                    x: entity.center.x + entity.radius, 
                    y: entity.center.y + entity.radius 
                }
            }
        };

        closedShapes.push(shapeInfo);

    }

    /**
     * 检查SOLID实体是否可以作为封闭图形
     */
    function checkSolidAsClosedShape(entity, data, parentGroup) {
        var points = [];

        // 优先使用corners，其次使用points
        if (entity.corners && entity.corners.length >= 3) {
            points = entity.corners.map(function(corner) {
                return { x: corner.x, y: corner.y, z: corner.z || 0 };
            });
        } else if (entity.points && entity.points.length >= 3) {
            points = entity.points.map(function(point) {
                return { x: point.x, y: point.y, z: point.z || 0 };
            });
        }

        if (points.length < 3) {
            return;
        }

        // 应用父组变换（如果存在）
        if (parentGroup) {
            points = points.map(function(point) {
                var tempVector = new THREE.Vector3(point.x, point.y, point.z);
                tempVector.applyMatrix4(parentGroup.matrixWorld);
                return { x: tempVector.x, y: tempVector.y, z: tempVector.z };
            });
        }

        var shapeInfo = {
            id: 'dxf_solid_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
            type: 'SOLID',
            layer: entity.layer || '0',
            points: points,
            entity: entity,
            area: calculateShapeArea(points),
            bounds: calculateShapeBounds(points)
        };

        closedShapes.push(shapeInfo);
    }


}