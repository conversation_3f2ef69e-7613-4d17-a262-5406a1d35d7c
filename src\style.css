* {
  margin: 0;
  padding: 0;
}

/* html {
  font-size: 14.4px;
}

@media screen and (min-width: 1920px) {
  html {
    font-size: 16.4px; 
  }
}

@media screen and (min-width: 1680px) and (max-width: 1919px) {
  html {
    font-size: 15.4px; 
  }
}

@media screen and (min-width: 1440px) and (max-width: 1679px) {
  html {
    font-size: 14.4px; 
  }
}

@media screen and (min-width: 1200px) and (max-width: 1439px) {
  html {
    font-size: 12px; 
  }
}

@media screen and (min-width: 1024px) and (max-width: 1199px) {
  html {
    font-size: 10.24px; 
  }
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
  html {
    font-size: 7.68px;
  }
}

@media screen and (max-width: 767px) {
  html {
    font-size: 6.4px; 
  }
} */


body * {
  box-sizing: border-box;
  flex-shrink: 0;
}
body {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahom<PERSON>,
    <PERSON><PERSON>, <PERSON><PERSON><PERSON> SC-Light, <PERSON> YaHei;
}
input {
  background-color: transparent;
  border: 0;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}

button:active {
  opacity: 0.6;
}
.van-nav-bar__left:active,
.van-nav-bar__right:active {
  opacity: 1;
}
[class*='van-']::after {
  border-bottom: 0;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-center {
  display: flex;
  justify-content: center;
}

.justify-end {
  display: flex;
  justify-content: flex-end;
}
.justify-evenly {
  display: flex;
  justify-content: space-evenly;
}
.justify-around {
  display: flex;
  justify-content: space-around;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.align-start {
  display: flex;
  align-items: flex-start;
}
.align-center {
  display: flex;
  align-items: center;
}
.align-end {
  display: flex;
  align-items: flex-end;
}
