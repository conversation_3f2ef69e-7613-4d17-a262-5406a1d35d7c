{"name": "chang<PERSON>", "private": true, "version": "0.0.0", "type": "module", "workspaces": ["packages/*"], "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "build:dev": "vue-tsc -b && vite build --mode development", "build:prod": "vue-tsc -b && vite build --mode production", "preview": "vite preview"}, "dependencies": {"@dxfom/mtext": "^0.4.0", "@thatopen/components": "^2.4.11", "@thatopen/components-front": "^2.4.11", "@thatopen/fragments": "^3.0.7", "@thatopen/ui": "^2.4.5", "axios": "^1.9.0", "bignumber.js": "^9.3.0", "dxf-parser": "^1.1.2", "element-plus": "^2.9.11", "less": "^4.3.0", "pinia": "^3.0.2", "stats.js": "^0.17.0", "three": "^0.176.0", "troika-three-text": "^0.52.4", "vue": "^3.5.13", "vue-router": "^4.5.1", "web-ifc": "^0.0.68"}, "devDependencies": {"@types/stats.js": "^0.17.4", "@types/three": "^0.176.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "postcss-px-to-viewport-8-plugin": "^1.2.5", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}